"use client"

import { useEffect } from 'react'
import Head from 'next/head'

interface MTCCLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
  showParticles?: boolean
  className?: string
}

export function MTCCLayout({ 
  children, 
  title = "Military Transition Command Center",
  description = "Empowering service members with seamless tools and resources to navigate civilian life with confidence.",
  showParticles = true,
  className = ""
}: MTCCLayoutProps) {
  
  useEffect(() => {
    // Load particles.js if needed
    if (showParticles && typeof window !== 'undefined') {
      const script = document.createElement('script')
      script.src = 'https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js'
      script.onload = () => {
        // Initialize particles
        if (window.particlesJS) {
          window.particlesJS("particles-js", {
            particles: {
              number: { value: 100, density: { enable: true, value_area: 800 } },
              color: { value: "#ffffff" },
              shape: { type: "star", stroke: { width: 0, color: "#000" } },
              opacity: { value: 0.3, random: true },
              size: { value: 2.5, random: true },
              line_linked: { enable: false },
              move: { 
                enable: true, 
                speed: 0.6, 
                direction: "none", 
                out_mode: "out" 
              }
            },
            interactivity: {
              detect_on: "canvas",
              events: { 
                onhover: { enable: true, mode: "repulse" }, 
                onclick: { enable: false } 
              },
              modes: { repulse: { distance: 80, duration: 0.4 } }
            },
            retina_detect: true
          })
        }
      }
      document.head.appendChild(script)
      
      return () => {
        document.head.removeChild(script)
      }
    }
  }, [showParticles])

  useEffect(() => {
    // Load Lucide icons
    const script = document.createElement('script')
    script.src = 'https://unpkg.com/lucide@latest'
    script.onload = () => {
      if (window.lucide) {
        window.lucide.createIcons()
      }
    }
    document.head.appendChild(script)
    
    return () => {
      document.head.removeChild(script)
    }
  }, [])

  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        
        {/* Fonts */}
        <link 
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" 
          rel="stylesheet" 
        />
        
        {/* Tailwind CSS */}
        <script src="https://cdn.tailwindcss.com"></script>
        
        {/* Custom Theme */}
        <link rel="stylesheet" href="/styles/mtcc-theme.css" />
      </Head>
      
      <div className={`min-h-screen bg-neutral-900 text-neutral-100 font-inter relative ${className}`}>
        {/* Particles Background */}
        {showParticles && (
          <div id="particles-js" className="absolute inset-0 -z-10"></div>
        )}
        
        {/* Main Content */}
        <main className="relative z-10">
          {children}
        </main>
      </div>
    </>
  )
}

// Navigation Component
interface MTCCNavProps {
  currentPage?: string
  onNavigate?: (page: string) => void
}

export function MTCCNav({ currentPage, onNavigate }: MTCCNavProps) {
  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: 'home' },
    { id: 'family', label: 'Family', icon: 'users' },
    { id: 'questionnaire', label: 'Assessment', icon: 'clipboard-list' },
    { id: 'benefits', label: 'Benefits', icon: 'shield' },
    { id: 'resources', label: 'Resources', icon: 'book-open' },
    { id: 'timeline', label: 'Timeline', icon: 'calendar' },
  ]

  return (
    <nav className="glass-nav sticky top-0 z-50 px-6 py-4">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Logo */}
        <div className="flex items-center space-x-3 animate-delayed-1">
          <i data-lucide="shield" className="w-8 h-8 text-blue-400"></i>
          <span className="font-montserrat font-semibold text-xl tracking-tight">
            MTCC
          </span>
        </div>
        
        {/* Navigation Links */}
        <div className="hidden md:flex items-center space-x-8">
          {navItems.map((item, index) => (
            <button
              key={item.id}
              onClick={() => onNavigate?.(item.id)}
              className={`nav-link flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300 animate-delayed-${index + 2} ${
                currentPage === item.id ? 'active bg-blue-500/10' : 'hover:bg-neutral-800/50'
              }`}
            >
              <i data-lucide={item.icon} className="w-4 h-4"></i>
              <span className="font-medium">{item.label}</span>
            </button>
          ))}
        </div>
        
        {/* User Menu */}
        <div className="flex items-center space-x-4 animate-delayed-6">
          <button className="p-2 rounded-lg hover:bg-neutral-800/50 transition-colors">
            <i data-lucide="bell" className="w-5 h-5 text-neutral-400"></i>
          </button>
          <button className="p-2 rounded-lg hover:bg-neutral-800/50 transition-colors">
            <i data-lucide="settings" className="w-5 h-5 text-neutral-400"></i>
          </button>
          <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
            <i data-lucide="user" className="w-4 h-4 text-white"></i>
          </div>
        </div>
      </div>
    </nav>
  )
}

// Page Header Component
interface MTCCPageHeaderProps {
  title: string
  subtitle?: string
  icon?: string
  actions?: React.ReactNode
}

export function MTCCPageHeader({ title, subtitle, icon, actions }: MTCCPageHeaderProps) {
  return (
    <div className="px-6 py-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between">
          <div className="animate-delayed-1">
            <div className="flex items-center space-x-4 mb-2">
              {icon && (
                <div className="w-12 h-12 rounded-xl bg-blue-500/10 border border-blue-500/20 flex items-center justify-center">
                  <i data-lucide={icon} className="w-6 h-6 text-blue-400"></i>
                </div>
              )}
              <h1 className="font-montserrat font-semibold text-3xl lg:text-4xl tracking-tight text-white text-shadow">
                {title}
              </h1>
            </div>
            {subtitle && (
              <p className="text-neutral-300 text-lg max-w-2xl">
                {subtitle}
              </p>
            )}
          </div>
          
          {actions && (
            <div className="animate-delayed-2">
              {actions}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Card Component
interface MTCCCardProps {
  children: React.ReactNode
  className?: string
  hover?: boolean
  glow?: boolean
}

export function MTCCCard({ children, className = "", hover = true, glow = false }: MTCCCardProps) {
  return (
    <div className={`
      card-primary 
      ${hover ? 'hover-lift' : ''} 
      ${glow ? 'border-glow' : ''} 
      ${className}
    `}>
      {children}
    </div>
  )
}

// Declare global types for external libraries
declare global {
  interface Window {
    particlesJS: any
    lucide: any
  }
}
