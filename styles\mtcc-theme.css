/* MTCC Design System - Military Transition Command Center */

/* Import Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap');

/* Custom Animations */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Animation Classes */
.animate-delayed-1 { animation: fadeInUp 0.8s 0.1s both; }
.animate-delayed-2 { animation: fadeInUp 0.8s 0.25s both; }
.animate-delayed-3 { animation: fadeInUp 0.8s 0.4s both; }
.animate-delayed-4 { animation: fadeInUp 0.8s 0.55s both; }
.animate-delayed-5 { animation: fadeInUp 0.8s 0.7s both; }
.animate-slide-left { animation: slideInLeft 0.6s ease-out; }
.animate-pulse-slow { animation: pulse 2s infinite; }

/* Font Utilities */
.font-montserrat { 
  font-family: 'Montserrat', sans-serif !important; 
}
.font-inter { 
  font-family: 'Inter', sans-serif !important; 
}

/* Background Patterns */
.bg-flag {
  background-image: url('https://images.unsplash.com/photo-1529619752077-8e0e3ff14516?auto=format&fit=crop&w=1200&q=80');
  background-size: cover;
  background-position: center;
}

.bg-military-pattern {
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.1) 1px, transparent 0);
  background-size: 20px 20px;
}

/* Glass Morphism Effects */
.glass-card {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(64, 64, 64, 0.3);
}

.glass-nav {
  background: rgba(23, 23, 23, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(64, 64, 64, 0.2);
}

/* Custom Toggle Switches */
.toggle {
  width: 2.75rem;
  height: 1.5rem;
}

.toggle-dot {
  transition: transform 0.25s ease;
}

input:checked + .toggle-dot {
  transform: translateX(1.25rem);
  background-color: #3b82f6;
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(64, 64, 64, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* Status Indicators */
.status-active {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.status-warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.status-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.status-info {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Button Variants */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.btn-secondary {
  background: rgba(64, 64, 64, 0.8);
  color: #e5e5e5;
  border: 1px solid rgba(115, 115, 115, 0.3);
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(82, 82, 82, 0.9);
  border-color: rgba(115, 115, 115, 0.5);
  transform: translateY(-1px);
}

/* Card Variants */
.card-primary {
  background: rgba(38, 38, 38, 0.8);
  border: 1px solid rgba(64, 64, 64, 0.3);
  backdrop-filter: blur(16px);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.card-primary:hover {
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

/* Navigation Styles */
.nav-link {
  color: #a3a3a3;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #3b82f6;
}

.nav-link.active {
  color: #3b82f6;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #2563eb);
  border-radius: 1px;
}

/* Form Styles */
.form-input {
  background: rgba(64, 64, 64, 0.6);
  border: 1px solid rgba(115, 115, 115, 0.3);
  color: #e5e5e5;
  border-radius: 8px;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(64, 64, 64, 0.8);
}

.form-input::placeholder {
  color: #737373;
}

/* Progress Bars */
.progress-bar {
  background: rgba(64, 64, 64, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
  height: 100%;
  border-radius: 8px;
  transition: width 0.6s ease;
}

/* Utility Classes */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.border-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
}

.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
}

/* Responsive Breakpoints */
@media (max-width: 768px) {
  .animate-delayed-1,
  .animate-delayed-2,
  .animate-delayed-3,
  .animate-delayed-4,
  .animate-delayed-5 {
    animation-delay: 0s;
    animation-duration: 0.5s;
  }
}
