"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MTCCNav, MTCCPageHeader, MTCCCard } from "@/components/layout/mtcc-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

interface Question {
  id: string
  category: string
  question: string
  type: "multiple-choice" | "scale" | "text"
  options?: string[]
  required: boolean
}

interface QuestionnaireSection {
  id: string
  title: string
  description: string
  icon: string
  questions: Question[]
  completed: boolean
  progress: number
}

export function QuestionnaireDashboard() {
  const [currentSection, setCurrentSection] = useState<string | null>(null)
  const [answers, setAnswers] = useState<Record<string, any>>({})

  const sections: QuestionnaireSection[] = [
    {
      id: "personal",
      title: "Personal Information",
      description: "Basic information about your military service and transition goals",
      icon: "user",
      completed: false,
      progress: 0,
      questions: [
        {
          id: "service-branch",
          category: "Military Background",
          question: "Which branch of the military did you serve in?",
          type: "multiple-choice",
          options: ["Army", "Navy", "Air Force", "Marines", "Coast Guard", "Space Force"],
          required: true
        },
        {
          id: "service-years",
          category: "Military Background", 
          question: "How many years did you serve?",
          type: "multiple-choice",
          options: ["Less than 4 years", "4-8 years", "8-12 years", "12-20 years", "20+ years"],
          required: true
        },
        {
          id: "transition-timeline",
          category: "Transition Planning",
          question: "When are you planning to transition?",
          type: "multiple-choice",
          options: ["Within 6 months", "6-12 months", "1-2 years", "2+ years", "Already transitioned"],
          required: true
        }
      ]
    },
    {
      id: "career",
      title: "Career & Employment",
      description: "Your career goals and employment preferences for civilian life",
      icon: "briefcase",
      completed: false,
      progress: 0,
      questions: [
        {
          id: "career-field",
          category: "Career Goals",
          question: "What career field are you most interested in pursuing?",
          type: "multiple-choice",
          options: ["Technology", "Healthcare", "Education", "Business", "Government", "Non-profit", "Other"],
          required: true
        },
        {
          id: "job-search-confidence",
          category: "Employment Readiness",
          question: "How confident are you in your job search abilities?",
          type: "scale",
          required: true
        },
        {
          id: "skills-transfer",
          category: "Skills Assessment",
          question: "Describe your most valuable transferable skills from military service:",
          type: "text",
          required: true
        }
      ]
    },
    {
      id: "education",
      title: "Education & Training",
      description: "Educational goals and training needs for your transition",
      icon: "graduation-cap",
      completed: false,
      progress: 0,
      questions: [
        {
          id: "education-level",
          category: "Current Education",
          question: "What is your highest level of education?",
          type: "multiple-choice",
          options: ["High School", "Some College", "Associate Degree", "Bachelor's Degree", "Master's Degree", "Doctoral Degree"],
          required: true
        },
        {
          id: "education-goals",
          category: "Future Education",
          question: "Are you planning to pursue additional education?",
          type: "multiple-choice",
          options: ["Yes, immediately", "Yes, within 2 years", "Maybe in the future", "No"],
          required: true
        }
      ]
    },
    {
      id: "benefits",
      title: "Benefits & Healthcare",
      description: "Understanding and planning for your military benefits transition",
      icon: "shield",
      completed: false,
      progress: 0,
      questions: [
        {
          id: "va-benefits-knowledge",
          category: "VA Benefits",
          question: "How familiar are you with VA benefits available to you?",
          type: "scale",
          required: true
        },
        {
          id: "healthcare-plan",
          category: "Healthcare",
          question: "What is your plan for healthcare coverage after transition?",
          type: "multiple-choice",
          options: ["VA Healthcare", "Employer Insurance", "COBRA", "ACA Marketplace", "Unsure"],
          required: true
        }
      ]
    }
  ]

  const handleAnswerChange = (questionId: string, value: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }))
  }

  const calculateProgress = (sectionId: string) => {
    const section = sections.find(s => s.id === sectionId)
    if (!section) return 0
    
    const answeredQuestions = section.questions.filter(q => answers[q.id] !== undefined)
    return (answeredQuestions.length / section.questions.length) * 100
  }

  const renderQuestion = (question: Question) => {
    const value = answers[question.id]

    switch (question.type) {
      case "multiple-choice":
        return (
          <RadioGroup value={value} onValueChange={(val) => handleAnswerChange(question.id, val)}>
            <div className="space-y-3">
              {question.options?.map((option) => (
                <div key={option} className="flex items-center space-x-3">
                  <RadioGroupItem value={option} id={`${question.id}-${option}`} />
                  <Label htmlFor={`${question.id}-${option}`} className="text-neutral-300 cursor-pointer">
                    {option}
                  </Label>
                </div>
              ))}
            </div>
          </RadioGroup>
        )
      
      case "scale":
        return (
          <div className="space-y-4">
            <div className="flex justify-between text-sm text-neutral-400">
              <span>Not confident</span>
              <span>Very confident</span>
            </div>
            <div className="flex space-x-2">
              {[1, 2, 3, 4, 5].map((num) => (
                <button
                  key={num}
                  onClick={() => handleAnswerChange(question.id, num)}
                  className={`w-12 h-12 rounded-full border-2 transition-all ${
                    value === num
                      ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                      : 'border-neutral-600 hover:border-neutral-500 text-neutral-400'
                  }`}
                >
                  {num}
                </button>
              ))}
            </div>
          </div>
        )
      
      case "text":
        return (
          <Textarea
            value={value || ""}
            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            className="form-input min-h-[100px]"
            placeholder="Please provide your answer..."
          />
        )
      
      default:
        return null
    }
  }

  if (currentSection) {
    const section = sections.find(s => s.id === currentSection)
    if (!section) return null

    return (
      <MTCCLayout title="Assessment Questionnaire • MTCC" showParticles={true}>
        <MTCCNav currentPage="questionnaire" />
        
        <MTCCPageHeader
          title={section.title}
          subtitle={section.description}
          icon={section.icon}
          actions={
            <Button 
              onClick={() => setCurrentSection(null)}
              className="btn-secondary"
            >
              <i data-lucide="arrow-left" className="w-4 h-4 mr-2"></i>
              Back to Overview
            </Button>
          }
        />

        <div className="px-6 pb-8">
          <div className="max-w-4xl mx-auto">
            {/* Progress */}
            <MTCCCard className="p-6 mb-8 animate-delayed-1">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-montserrat font-semibold text-lg text-white">Progress</h3>
                <span className="text-blue-400 font-semibold">
                  {Math.round(calculateProgress(currentSection))}% Complete
                </span>
              </div>
              <div className="progress-bar h-3">
                <div 
                  className="progress-fill h-full transition-all duration-500"
                  style={{ width: `${calculateProgress(currentSection)}%` }}
                ></div>
              </div>
            </MTCCCard>

            {/* Questions */}
            <div className="space-y-6">
              {section.questions.map((question, index) => (
                <MTCCCard key={question.id} className={`p-6 animate-delayed-${Math.min(index + 2, 5)}`}>
                  <div className="mb-4">
                    <Badge className="status-info text-xs px-2 py-1 rounded-full mb-2">
                      {question.category}
                    </Badge>
                    <h4 className="font-montserrat font-medium text-lg text-white mb-2">
                      {question.question}
                      {question.required && <span className="text-red-400 ml-1">*</span>}
                    </h4>
                  </div>
                  
                  {renderQuestion(question)}
                </MTCCCard>
              ))}
            </div>

            {/* Save Progress */}
            <div className="mt-8 flex justify-center animate-delayed-6">
              <Button className="btn-primary px-8">
                <i data-lucide="save" className="w-4 h-4 mr-2"></i>
                Save Progress
              </Button>
            </div>
          </div>
        </div>
      </MTCCLayout>
    )
  }

  return (
    <MTCCLayout title="Assessment Questionnaire • MTCC" showParticles={true}>
      <MTCCNav currentPage="questionnaire" />
      
      <MTCCPageHeader
        title="Transition Assessment"
        subtitle="Complete these questionnaires to receive personalized recommendations for your military transition"
        icon="clipboard-list"
      />

      <div className="px-6 pb-8">
        <div className="max-w-6xl mx-auto">
          {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <MTCCCard className="p-6 text-center animate-delayed-1">
              <div className="w-12 h-12 rounded-xl bg-blue-500/10 border border-blue-500/20 flex items-center justify-center mx-auto mb-3">
                <i data-lucide="clipboard-list" className="w-6 h-6 text-blue-400"></i>
              </div>
              <h3 className="font-montserrat font-semibold text-lg text-white mb-1">
                {sections.length}
              </h3>
              <p className="text-neutral-400 text-sm">Total Sections</p>
            </MTCCCard>

            <MTCCCard className="p-6 text-center animate-delayed-2">
              <div className="w-12 h-12 rounded-xl bg-green-500/10 border border-green-500/20 flex items-center justify-center mx-auto mb-3">
                <i data-lucide="check-circle" className="w-6 h-6 text-green-400"></i>
              </div>
              <h3 className="font-montserrat font-semibold text-lg text-white mb-1">
                {sections.filter(s => s.completed).length}
              </h3>
              <p className="text-neutral-400 text-sm">Completed</p>
            </MTCCCard>

            <MTCCCard className="p-6 text-center animate-delayed-3">
              <div className="w-12 h-12 rounded-xl bg-yellow-500/10 border border-yellow-500/20 flex items-center justify-center mx-auto mb-3">
                <i data-lucide="clock" className="w-6 h-6 text-yellow-400"></i>
              </div>
              <h3 className="font-montserrat font-semibold text-lg text-white mb-1">
                ~15 min
              </h3>
              <p className="text-neutral-400 text-sm">Est. Time</p>
            </MTCCCard>

            <MTCCCard className="p-6 text-center animate-delayed-4">
              <div className="w-12 h-12 rounded-xl bg-purple-500/10 border border-purple-500/20 flex items-center justify-center mx-auto mb-3">
                <i data-lucide="target" className="w-6 h-6 text-purple-400"></i>
              </div>
              <h3 className="font-montserrat font-semibold text-lg text-white mb-1">
                Personal
              </h3>
              <p className="text-neutral-400 text-sm">Recommendations</p>
            </MTCCCard>
          </div>

          {/* Questionnaire Sections */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {sections.map((section, index) => (
              <MTCCCard key={section.id} className={`p-6 cursor-pointer animate-delayed-${Math.min(index + 1, 5)}`} onClick={() => setCurrentSection(section.id)}>
                <div className="flex items-start space-x-4">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                    section.completed 
                      ? 'bg-green-500/20 border border-green-500/30' 
                      : 'bg-blue-500/10 border border-blue-500/20'
                  }`}>
                    <i data-lucide={section.completed ? 'check' : section.icon} className={`w-6 h-6 ${
                      section.completed ? 'text-green-400' : 'text-blue-400'
                    }`}></i>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-montserrat font-semibold text-lg text-white">
                        {section.title}
                      </h3>
                      {section.completed && (
                        <Badge className="status-active text-xs px-2 py-1 rounded-full">
                          Complete
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-neutral-400 text-sm mb-4">
                      {section.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-neutral-500">
                        {section.questions.length} questions
                      </span>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-blue-400">
                          {Math.round(calculateProgress(section.id))}%
                        </span>
                        <div className="w-16 h-2 progress-bar">
                          <div 
                            className="progress-fill h-full"
                            style={{ width: `${calculateProgress(section.id)}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <i data-lucide="chevron-right" className="w-5 h-5 text-neutral-400"></i>
                </div>
              </MTCCCard>
            ))}
          </div>
        </div>
      </div>
    </MTCCLayout>
  )
}
