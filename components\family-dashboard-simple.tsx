"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Heart, Users, Calendar, AlertTriangle, CheckCircle, Plus, X, Clock } from "lucide-react"

interface FamilyMember {
  id: string
  name: string
  relationship: string
  age?: number
  status: string
  tasks: number
  completedTasks: number
  concerns: string[]
}

interface FamilyTask {
  id: string
  title: string
  assignedTo: string
  dueDate: string
  status: "completed" | "in-progress" | "not-started"
  priority: "high" | "medium" | "low"
}

// Storage keys for localStorage
const STORAGE_KEYS = {
  FAMILY_MEMBERS: 'mtcc_family_members',
  FAMILY_TASKS: 'mtcc_family_tasks'
}

// Default data
const DEFAULT_FAMILY_MEMBERS: FamilyMember[] = [
  {
    id: "1",
    name: "Sarah Johnson",
    relationship: "Spouse",
    status: "Active",
    tasks: 3,
    completedTasks: 2,
    concerns: ["Healthcare transition", "Job search support"],
  },
  {
    id: "2",
    name: "Michael Johnson",
    relationship: "Son",
    age: 16,
    status: "Student",
    tasks: 2,
    completedTasks: 1,
    concerns: ["School transfer", "College planning"],
  }
]

const DEFAULT_FAMILY_TASKS: FamilyTask[] = [
  {
    id: "1",
    title: "Update DEERS Information",
    assignedTo: "Spouse",
    dueDate: "Jul 15, 2024",
    status: "completed",
    priority: "high",
  },
  {
    id: "2",
    title: "Research School Districts",
    assignedTo: "Both Parents",
    dueDate: "Jul 20, 2024",
    status: "in-progress",
    priority: "high",
  }
]

// Storage utility functions
const saveToStorage = (key: string, data: any) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (error) {
    console.error('Error saving to localStorage:', error)
  }
}

const loadFromStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const saved = localStorage.getItem(key)
    if (saved) {
      return JSON.parse(saved)
    }
  } catch (error) {
    console.error('Error loading from localStorage:', error)
  }
  return defaultValue
}

export function FamilyDashboard() {
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([])
  const [familyTasks, setFamilyTasks] = useState<FamilyTask[]>([])
  const [isLoaded, setIsLoaded] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  
  const [newMember, setNewMember] = useState({
    name: "",
    relationship: "",
    age: "",
    status: "",
    concerns: ""
  })

  // Load data from localStorage on component mount
  useEffect(() => {
    const loadedMembers = loadFromStorage(STORAGE_KEYS.FAMILY_MEMBERS, DEFAULT_FAMILY_MEMBERS)
    const loadedTasks = loadFromStorage(STORAGE_KEYS.FAMILY_TASKS, DEFAULT_FAMILY_TASKS)
    
    setFamilyMembers(loadedMembers)
    setFamilyTasks(loadedTasks)
    setIsLoaded(true)
  }, [])

  const handleAddMember = () => {
    if (!newMember.name.trim() || !newMember.relationship.trim()) {
      return
    }

    const member: FamilyMember = {
      id: Date.now().toString(),
      name: newMember.name.trim(),
      relationship: newMember.relationship,
      age: newMember.age ? parseInt(newMember.age) : undefined,
      status: newMember.status || "Active",
      tasks: 0,
      completedTasks: 0,
      concerns: newMember.concerns
        ? newMember.concerns.split(',').map(c => c.trim()).filter(c => c.length > 0)
        : []
    }

    const updatedMembers = [...familyMembers, member]
    setFamilyMembers(updatedMembers)
    saveToStorage(STORAGE_KEYS.FAMILY_MEMBERS, updatedMembers)
    
    setNewMember({
      name: "",
      relationship: "",
      age: "",
      status: "",
      concerns: ""
    })
    setIsAddDialogOpen(false)
  }

  const handleRemoveMember = (id: string) => {
    const updatedMembers = familyMembers.filter(member => member.id !== id)
    setFamilyMembers(updatedMembers)
    saveToStorage(STORAGE_KEYS.FAMILY_MEMBERS, updatedMembers)
  }

  const handleClearAllData = () => {
    if (confirm('Are you sure you want to clear all family data? This cannot be undone.')) {
      setFamilyMembers(DEFAULT_FAMILY_MEMBERS)
      setFamilyTasks(DEFAULT_FAMILY_TASKS)
      
      saveToStorage(STORAGE_KEYS.FAMILY_MEMBERS, DEFAULT_FAMILY_MEMBERS)
      saveToStorage(STORAGE_KEYS.FAMILY_TASKS, DEFAULT_FAMILY_TASKS)
    }
  }

  // Show loading state until data is loaded
  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-neutral-900 text-neutral-100 font-inter">
        <div className="flex items-center justify-center py-24">
          <div className="text-center animate-delayed-1">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <div className="text-white font-montserrat text-lg">Loading family dashboard...</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-neutral-900 text-neutral-100 font-inter">
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white font-montserrat">Family Dashboard</h1>
            <p className="text-neutral-400 text-sm mt-1">
              Manage your family members, tasks, and events with automatic data persistence
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleClearAllData}
              className="glass-button border-neutral-600 text-neutral-300 hover:bg-neutral-700/50"
            >
              Reset Data
            </Button>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Users className="w-4 h-4 mr-2" />
                  Add Family Member
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-neutral-800 border-neutral-700 text-white max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-white font-montserrat text-xl">
                    Add Family Member
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name" className="text-neutral-300 font-medium mb-2 block">
                      Full Name *
                    </Label>
                    <Input
                      id="name"
                      value={newMember.name}
                      onChange={(e) => setNewMember({ ...newMember, name: e.target.value })}
                      className="bg-neutral-700 border-neutral-600 text-white w-full"
                      placeholder="Enter full name"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="relationship" className="text-neutral-300 font-medium mb-2 block">
                      Relationship *
                    </Label>
                    <Select value={newMember.relationship} onValueChange={(value) => setNewMember({ ...newMember, relationship: value })}>
                      <SelectTrigger className="bg-neutral-700 border-neutral-600 text-white">
                        <SelectValue placeholder="Select relationship" />
                      </SelectTrigger>
                      <SelectContent className="bg-neutral-700 border-neutral-600">
                        <SelectItem value="Spouse">Spouse</SelectItem>
                        <SelectItem value="Son">Son</SelectItem>
                        <SelectItem value="Daughter">Daughter</SelectItem>
                        <SelectItem value="Parent">Parent</SelectItem>
                        <SelectItem value="Sibling">Sibling</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex justify-end space-x-3 pt-6">
                    <Button
                      variant="outline"
                      onClick={() => setIsAddDialogOpen(false)}
                      className="border-neutral-600 text-neutral-300 hover:bg-neutral-700"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAddMember}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                      disabled={!newMember.name.trim() || !newMember.relationship.trim()}
                    >
                      Add Member
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Family Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {familyMembers.map((member, index) => (
            <Card key={member.id} className="bg-neutral-800 border-neutral-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-full bg-red-500 flex items-center justify-center">
                    <Heart className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-montserrat font-semibold text-white text-lg">
                      {member.name}
                    </h3>
                    <Badge className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                      {member.relationship}
                    </Badge>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleRemoveMember(member.id)}
                  className="h-8 w-8 p-0 text-neutral-400 hover:text-red-400 hover:bg-red-900/20"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              <div className="space-y-3">
                {member.age && (
                  <div className="flex items-center justify-between">
                    <span className="text-neutral-400 text-sm">Age:</span>
                    <span className="text-white text-sm">{member.age}</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-neutral-400 text-sm">Status:</span>
                  <Badge className="bg-green-600 text-white text-xs">
                    {member.status}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-neutral-400 text-sm">Tasks:</span>
                  <span className="text-white text-sm">
                    {member.completedTasks}/{member.tasks}
                  </span>
                </div>
                <Progress
                  value={member.tasks > 0 ? (member.completedTasks / member.tasks) * 100 : 0}
                  className="h-2"
                />
                {member.concerns.length > 0 && (
                  <div className="mt-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                    <div className="flex items-center mb-2">
                      <AlertTriangle className="w-4 h-4 mr-2 text-yellow-400" />
                      <span className="text-yellow-300 text-sm font-medium">Current Concerns:</span>
                    </div>
                    <div className="space-y-1">
                      {member.concerns.map((concern, index) => (
                        <div key={index} className="flex items-start">
                          <div className="w-1.5 h-1.5 rounded-full bg-yellow-400 mt-2 mr-2 flex-shrink-0"></div>
                          <span className="text-yellow-200 text-xs">{concern}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
