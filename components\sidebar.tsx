"use client"

import {
  Home,
  Target,
  Bot,
  Users,
  MapPin,
  Heart,
  FileText,
  Calculator,
  Briefcase,
  Smartphone,
  Calendar,
  Shield,
  LogOut
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface SidebarProps {
  activeSection: string
  onSectionChange: (section: string) => void
  user: any
}

const navigationItems = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: Home,
    description: "Your command center overview",
  },
  {
    id: "mission-control",
    label: "Mission Control",
    icon: Target,
    description: "Strategic transition overview",
  },
  {
    id: "ai-coach",
    label: "AI Coach",
    icon: Bo<PERSON>,
    description: "Personalized guidance",
  },
  {
    id: "family-dashboard",
    label: "Family Hub",
    icon: Heart,
    description: "Family transition support",
  },
  {
    id: "state-benefits",
    label: "State Benefits",
    icon: MapPin,
    description: "Location-based benefits",
  },
  {
    id: "peer-mentorship",
    label: "Mentorship",
    icon: Users,
    description: "Connect with veterans",
  },
  {
    id: "va-tracker",
    label: "VA Tracker",
    icon: FileText,
    description: "Benefits and claims",
  },
  {
    id: "financial-calculator",
    label: "Financial Tools",
    icon: Calculator,
    description: "Retirement planning",
  },
  {
    id: "job-matching",
    label: "Job Matching",
    icon: Briefcase,
    description: "Career opportunities",
  },
  {
    id: "timeline",
    label: "Timeline",
    icon: Calendar,
    description: "Transition roadmap",
  },
  {
    id: "mobile-app",
    label: "Mobile App",
    icon: Smartphone,
    description: "Download our app",
  },
]

export function Sidebar({ activeSection, onSectionChange, user }: SidebarProps) {
  const handleLogout = () => {
    // This would typically call a logout function passed as prop
    window.location.reload()
  }

  return (
    <div className="w-64 glass-sidebar flex flex-col animate-slide-left">
      {/* Header */}
      <div className="p-6 border-b border-neutral-700/50">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center shadow-lg">
            <Shield className="w-7 h-7 text-white" />
          </div>
          <div>
            <h2 className="text-white font-semibold font-montserrat text-lg">MTCC</h2>
            <p className="text-neutral-400 text-xs">Command Center</p>
          </div>
        </div>
      </div>

      {/* User Info */}
      <div className="p-4 border-b border-neutral-700/50">
        <div className="glass-button p-3 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-neutral-600 to-neutral-700 rounded-full flex items-center justify-center border border-neutral-500">
              <span className="text-white text-sm font-semibold font-montserrat">
                {user?.firstName?.[0] || 'U'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-white text-sm font-medium truncate font-montserrat">
                {user?.rank} {user?.lastName || 'User'}
              </p>
              <p className="text-neutral-400 text-xs truncate">
                {user?.branch || 'Service Member'}
              </p>
            </div>
          </div>
          {user?.daysUntilSeparation && (
            <div className="mt-3">
              <div className="military-badge text-xs">
                {user.daysUntilSeparation} days to separation
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto p-4">
        <nav className="space-y-2">
          {navigationItems.map((item, index) => {
            const Icon = item.icon
            const isActive = activeSection === item.id

            return (
              <button
                key={item.id}
                type="button"
                onClick={() => onSectionChange(item.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-all duration-300 animate-delayed-${Math.min(index + 1, 5)} delay-${index * 100} ${
                  isActive
                    ? "bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg border border-blue-500/50"
                    : "text-neutral-300 hover:bg-neutral-700/50 hover:text-white glass-button"
                }`}
              >
                <Icon className="w-5 h-5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate font-montserrat">{item.label}</p>
                  <p className={`text-xs truncate ${
                    isActive ? "text-blue-100" : "text-neutral-400"
                  }`}>
                    {item.description}
                  </p>
                </div>
                {isActive && (
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                )}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-neutral-700/50">
        <Button
          onClick={handleLogout}
          variant="outline"
          size="sm"
          className="w-full glass-button border-neutral-600 text-neutral-300 hover:bg-neutral-700/50 hover:border-neutral-500 font-montserrat"
        >
          <LogOut className="w-4 h-4 mr-2" />
          Sign Out
        </Button>
      </div>
    </div>
  )
}
