{"css.validate": false, "less.validate": false, "scss.validate": false, "css.lint.unknownAtRules": "ignore", "css.lint.unknownProperties": "ignore", "tailwindCSS.includeLanguages": {"typescript": "typescript", "typescriptreact": "typescriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "editor.quickSuggestions": {"strings": true}, "files.associations": {"*.css": "tailwindcss"}}