"use client"

import { useEffect } from 'react'

interface MTCCThemeProviderProps {
  children: React.ReactNode
}

export function MTCCThemeProvider({ children }: MTCCThemeProviderProps) {
  useEffect(() => {
    // Force dark mode and MTCC theme on document
    document.documentElement.classList.add('dark')
    document.body.classList.add('bg-neutral-900', 'text-neutral-100', 'font-inter')
    
    // Apply MTCC theme to all existing elements
    const applyMTCCTheme = () => {
      // Force theme on all cards
      const cards = document.querySelectorAll('.card, [data-card]')
      cards.forEach(card => {
        if (card instanceof HTMLElement) {
          card.style.backgroundColor = 'var(--mtcc-neutral-800)'
          card.style.borderColor = 'var(--mtcc-neutral-700)'
          card.style.color = 'var(--mtcc-neutral-100)'
        }
      })
      
      // Force theme on all buttons
      const buttons = document.querySelectorAll('button')
      buttons.forEach(button => {
        if (button instanceof HTMLElement) {
          button.style.fontFamily = 'Inter, sans-serif'
        }
      })
      
      // Force theme on all inputs
      const inputs = document.querySelectorAll('input, textarea, select')
      inputs.forEach(input => {
        if (input instanceof HTMLElement) {
          if (!input.classList.contains('form-input')) {
            input.style.backgroundColor = 'var(--mtcc-neutral-700)'
            input.style.borderColor = 'var(--mtcc-neutral-600)'
            input.style.color = 'var(--mtcc-neutral-100)'
          }
        }
      })
      
      // Force theme on all dialogs
      const dialogs = document.querySelectorAll('[role="dialog"], .dialog-content')
      dialogs.forEach(dialog => {
        if (dialog instanceof HTMLElement) {
          dialog.style.backgroundColor = 'var(--mtcc-neutral-800)'
          dialog.style.borderColor = 'var(--mtcc-neutral-700)'
          dialog.style.color = 'var(--mtcc-neutral-100)'
        }
      })
    }
    
    // Apply theme immediately
    applyMTCCTheme()
    
    // Apply theme when DOM changes
    const observer = new MutationObserver(applyMTCCTheme)
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class']
    })
    
    return () => {
      observer.disconnect()
    }
  }, [])

  return <>{children}</>
}

// MTCC Theme Configuration
export const MTCCTheme = {
  colors: {
    neutral: {
      900: '#171717',
      800: '#262626',
      700: '#404040',
      600: '#525252',
      500: '#737373',
      400: '#a3a3a3',
      300: '#d4d4d4',
      200: '#e5e5e5',
      100: '#f5f5f5',
    },
    blue: {
      500: '#3b82f6',
      400: '#60a5fa',
      600: '#2563eb',
      300: '#93c5fd',
    },
    red: {
      500: '#ef4444',
      400: '#f87171',
      600: '#dc2626',
    },
    green: {
      500: '#10b981',
      400: '#34d399',
      600: '#059669',
    },
    yellow: {
      500: '#f59e0b',
      400: '#fbbf24',
      300: '#fcd34d',
    },
  },
  fonts: {
    primary: 'Inter, sans-serif',
    heading: 'Montserrat, sans-serif',
  },
  animations: {
    fadeInUp: 'fadeInUp 0.8s ease-out',
    slideInLeft: 'slideInLeft 0.6s ease-out',
    pulse: 'pulse 2s infinite',
  },
  shadows: {
    glow: '0 0 20px rgba(59, 130, 246, 0.2)',
    card: '0 8px 32px rgba(0, 0, 0, 0.3)',
    button: '0 4px 14px rgba(59, 130, 246, 0.3)',
  },
}

// Utility function to get MTCC theme values
export const getMTCCColor = (path: string) => {
  const keys = path.split('.')
  let value: any = MTCCTheme.colors
  
  for (const key of keys) {
    value = value[key]
    if (value === undefined) return null
  }
  
  return value
}

// MTCC Component Classes
export const MTCCClasses = {
  // Layout
  page: 'min-h-screen bg-neutral-900 text-neutral-100 font-inter',
  container: 'max-w-7xl mx-auto px-6',
  
  // Cards
  card: 'card-primary p-6',
  cardHover: 'card-primary p-6 hover-lift',
  glassCard: 'glass-card p-6',
  
  // Buttons
  buttonPrimary: 'btn-primary px-4 py-2 rounded-lg font-medium',
  buttonSecondary: 'btn-secondary px-4 py-2 rounded-lg font-medium',
  buttonGhost: 'glass-button px-4 py-2 rounded-lg font-medium',
  
  // Forms
  input: 'form-input w-full',
  label: 'text-neutral-300 font-medium mb-2 block',
  
  // Status
  statusActive: 'status-active text-xs px-2 py-1 rounded-full',
  statusWarning: 'status-warning text-xs px-2 py-1 rounded-full',
  statusDanger: 'status-danger text-xs px-2 py-1 rounded-full',
  statusInfo: 'status-info text-xs px-2 py-1 rounded-full',
  
  // Typography
  heading1: 'font-montserrat font-semibold text-3xl lg:text-4xl text-white text-shadow',
  heading2: 'font-montserrat font-semibold text-2xl lg:text-3xl text-white',
  heading3: 'font-montserrat font-semibold text-xl lg:text-2xl text-white',
  body: 'font-inter text-neutral-300',
  bodySecondary: 'font-inter text-neutral-400 text-sm',
  
  // Navigation
  navLink: 'nav-link px-3 py-2 rounded-lg transition-all duration-300',
  navLinkActive: 'nav-link active px-3 py-2 rounded-lg bg-blue-500/10',
  
  // Animations
  fadeIn1: 'animate-delayed-1',
  fadeIn2: 'animate-delayed-2',
  fadeIn3: 'animate-delayed-3',
  fadeIn4: 'animate-delayed-4',
  fadeIn5: 'animate-delayed-5',
  
  // Utilities
  textShadow: 'text-shadow',
  borderGlow: 'border-glow',
  hoverLift: 'hover-lift',
}

// MTCC Theme Hook
export const useMTCCTheme = () => {
  return {
    theme: MTCCTheme,
    classes: MTCCClasses,
    getColor: getMTCCColor,
  }
}
