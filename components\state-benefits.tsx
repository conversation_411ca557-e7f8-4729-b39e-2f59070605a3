"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MapPin, DollarSign, GraduationCap, Car, Home, Heart } from "lucide-react"

interface StateBenefitsProps {
  user?: any
}

interface EstimatedSavings {
  tax: number;
  taxDescription: string;
  education: number;
  educationDescription: string;
  other: number;
  otherDescription: string;
}

interface StateBenefitData {
  name: string;
  taxBenefits: string[];
  education: string[];
  vehicle: string[];
  recreation: string[];
  other: string[];
  estimatedSavings: EstimatedSavings;
}

export function StateBenefits({ user }: StateBenefitsProps) {
  const [selectedStateOne, setSelectedStateOne] = useState("florida")
  const [selectedStateTwo, setSelectedStateTwo] = useState<string | null>(null)

  const stateBenefits: Record<string, StateBenefitData> = {
    florida: {
      name: "Florida",
      taxBenefits: [
        "No state income tax",
        "Property tax exemption for 100% disabled veterans",
        "$5,000 property tax deduction for 10%+ disability",
        "No building permit fees for disabled veterans",
      ],
      education: [
        "Free tuition at state universities for Purple Heart recipients",
        "Disabled Veterans Tuition Waiver",
        "Yellow Ribbon GI Education Enhancement Program",
      ],
      vehicle: [
        "Free disabled veteran license plates",
        "Veteran designation on driver's license",
        "No driver's license fees for 100% disabled veterans",
      ],
      recreation: [
        "25% state park discount",
        "$10 state forest annual pass",
        "Free lifetime passes for disabled veterans",
        "$20 Military Gold Sportsman's License",
      ],
      other: ["Expedited concealed carry permits", "Business license fee waivers", "Hunting safety course exemptions"],
      estimatedSavings: {
        tax: 3200, taxDescription: "No state income tax & property tax benefits",
        education: 8500, educationDescription: "Tuition waivers/programs potential",
        other: 1800, otherDescription: "Vehicle, recreation, misc. benefits",
      },
    },
    texas: {
      name: "Texas",
      taxBenefits: [
        "No state income tax",
        "Property tax exemptions for disabled veterans",
        "Surviving spouse property tax benefits",
      ],
      education: [
        "Hazlewood Act - Free tuition for veterans",
        "Legacy Program for children",
        "Top 10% automatic admission waiver",
      ],
      vehicle: [
        "Disabled veteran license plates",
        "Veteran designation available",
        "Motorcycle license fee exemptions",
      ],
      recreation: [
        "Free state park entry for disabled veterans",
        "Hunting and fishing license discounts",
        "Special veteran hunting seasons",
      ],
      other: ["Veteran business loan programs", "Property tax exemptions", "Expedited professional licensing"],
      estimatedSavings: {
        tax: 4000, taxDescription: "Primarily from no state income tax",
        education: 10000, educationDescription: "Hazlewood Act potential",
        other: 1500, otherDescription: "License plates, park entry, etc.",
      },
    },
    virginia: {
      name: "Virginia",
      taxBenefits: [
        "Military retirement pay exemption",
        "Property tax relief for disabled veterans",
        "Surviving spouse tax benefits",
      ],
      education: [
        "In-state tuition for veterans",
        "Virginia Military Survivors and Dependents Education Program",
        "Community college benefits",
      ],
      vehicle: [
        "Special veteran license plates",
        "Disabled veteran parking privileges",
        "Vehicle registration discounts",
      ],
      recreation: ["State park discounts", "Hunting and fishing license benefits", "Golf course discounts"],
      other: ["Veteran business certifications", "Employment preferences", "Healthcare benefits"],
      estimatedSavings: {
        tax: 2800, taxDescription: "Retirement pay exemption & property tax relief",
        education: 7000, educationDescription: "In-state tuition & VMSDEP",
        other: 1200, otherDescription: "Vehicle, recreation, employment",
      },
    },
    california: {
      name: "California",
      taxBenefits: [
        "Property tax exemption for disabled veterans",
        "Veteran property tax postponement",
        "CalVet home loan program",
      ],
      education: [
        "Cal Vet fee waiver for state universities",
        "Community college enrollment priority",
        "Dependents education benefits",
      ],
      vehicle: [
        "Disabled veteran license plates",
        "Veteran designation on license",
        "Parking privileges for disabled veterans",
      ],
      recreation: ["State park discounts", "Hunting and fishing license benefits", "Golf course access"],
      other: ["Veteran business loan programs", "Employment preferences", "Healthcare benefits"],
      estimatedSavings: {
        tax: 2500, taxDescription: "Property tax exemptions & CalVet loans",
        education: 9000, educationDescription: "CalVet fee waiver & dependent benefits",
        other: 2000, otherDescription: "Vehicle, recreation, business loans",
      },
    },
    "north-carolina": {
      name: "North Carolina",
      taxBenefits: [
        "Military retirement pay deduction",
        "Property tax exclusion for disabled veterans",
        "Surviving spouse benefits",
      ],
      education: [
        "In-state tuition for veterans",
        "Scholarship programs for dependents",
        "Community college benefits",
      ],
      vehicle: [
        "Special veteran license plates",
        "Disabled veteran parking",
        "Registration fee discounts",
      ],
      recreation: ["State park benefits", "Hunting and fishing discounts", "Recreation facility access"],
      other: ["Veteran business certifications", "Employment assistance", "Healthcare programs"],
      estimatedSavings: {
        tax: 2200, taxDescription: "Retirement pay deduction & property tax exclusion",
        education: 6500, educationDescription: "In-state tuition & scholarships",
        other: 1000, otherDescription: "Vehicle, recreation, employment",
      },
    },
  }

  const costOfLiving = {
    florida: { housing: 95, utilities: 102, groceries: 98, transportation: 101, healthcare: 97, overall: 98 },
    texas: { housing: 88, utilities: 99, groceries: 91, transportation: 94, healthcare: 95, overall: 93 },
    virginia: { housing: 108, utilities: 103, groceries: 105, transportation: 102, healthcare: 101, overall: 104 },
    california: { housing: 158, utilities: 107, groceries: 112, transportation: 115, healthcare: 109, overall: 138 },
    "north-carolina": { housing: 92, utilities: 98, groceries: 96, transportation: 97, healthcare: 94, overall: 95 },
  }

  const stateOneData = stateBenefits[selectedStateOne as keyof typeof stateBenefits]
  const stateOneCOL = costOfLiving[selectedStateOne as keyof typeof costOfLiving]
  const stateOneSavings = stateOneData?.estimatedSavings

  const stateTwoData = selectedStateTwo ? stateBenefits[selectedStateTwo as keyof typeof stateBenefits] : null
  const stateTwoCOL = selectedStateTwo ? costOfLiving[selectedStateTwo as keyof typeof costOfLiving] : null
  const stateTwoSavings = stateTwoData?.estimatedSavings

  const showComparison = selectedStateTwo && selectedStateOne !== selectedStateTwo && stateTwoData && stateTwoCOL && stateTwoSavings;

  // Safety check - if primary state data is missing, show error message
  if (!stateOneData || !stateOneCOL || !stateOneSavings) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-white">State Benefits Calculator</h1>
        </div>
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6">
            <p className="text-white text-center">
              Sorry, data for the selected state is not available yet. Please select a different state.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between animate-delayed-1">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gradient-to-br from-green-600 to-blue-600 rounded-lg flex items-center justify-center">
            <MapPin className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white font-montserrat">State Benefits Calculator</h1>
            <p className="text-neutral-400">Discover veteran benefits in your target location</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedStateOne} onValueChange={setSelectedStateOne}>
            <SelectTrigger className="w-[180px] glass-card border-neutral-600 text-white">
              <SelectValue placeholder="Select State 1" />
            </SelectTrigger>
            <SelectContent className="glass-card border-neutral-700">
              {Object.keys(stateBenefits).map(key => (
                <SelectItem key={key} value={key}>{stateBenefits[key].name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedStateTwo || "none"} onValueChange={(value) => setSelectedStateTwo(value === "none" ? null : value)}>
            <SelectTrigger className="w-[180px] glass-card border-neutral-600 text-white">
              <SelectValue placeholder="Compare with (optional)" />
            </SelectTrigger>
            <SelectContent className="glass-card border-neutral-700">
              <SelectItem value="none">None</SelectItem>
              {Object.keys(stateBenefits)
                .filter(key => key !== selectedStateOne) // Optionally filter out the first selected state
                .map(key => (
                  <SelectItem key={key} value={key}>{stateBenefits[key].name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* State Overview */}
      <Card className="bg-neutral-800 border-neutral-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <MapPin className="w-5 h-5 mr-2" />
            {stateOneData.name} Veteran Benefits Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Tax Benefits */}
            <div>
              <h3 className="text-white font-semibold mb-3 flex items-center">
                <DollarSign className="w-4 h-4 mr-2" />
                Tax Benefits
              </h3>
              <div className="space-y-2">
                {stateOneData.taxBenefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-neutral-300 text-sm">{benefit}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Education Benefits */}
            <div>
              <h3 className="text-white font-semibold mb-3 flex items-center">
                <GraduationCap className="w-4 h-4 mr-2" />
                Education Benefits
              </h3>
              <div className="space-y-2">
                {stateOneData.education.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-neutral-300 text-sm">{benefit}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Vehicle Benefits */}
            <div>
              <h3 className="text-white font-semibold mb-3 flex items-center">
                <Car className="w-4 h-4 mr-2" />
                Vehicle Benefits
              </h3>
              <div className="space-y-2">
                {stateOneData.vehicle.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-neutral-300 text-sm">{benefit}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cost of Living Comparison */}
        <Card className="bg-neutral-800 border-neutral-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Home className="w-5 h-5 mr-2" />
              Cost of Living Index
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(stateOneCOL).map(([category, value]) => (
                <div key={category} className="flex items-center justify-between">
                  <span className="text-neutral-300 capitalize">{category}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-neutral-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${value > 100 ? "bg-red-400" : value > 95 ? "bg-yellow-400" : "bg-green-400"}`}
                        style={{ width: `${Math.min(value, 120)}%` }}
                      ></div>
                    </div>
                    <span
                      className={`text-sm font-medium ${value > 100 ? "text-red-400" : value > 95 ? "text-yellow-400" : "text-green-400"}`}
                    >
                      {value}
                    </span>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 p-3 bg-slate-700 rounded-lg">
              <p className="text-slate-300 text-sm">
                <strong>Note:</strong> Index based on national average of 100. Lower numbers indicate lower costs.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Additional Benefits */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Heart className="w-5 h-5 mr-2" />
              Additional Benefits
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="text-white font-medium mb-2">Recreation & Outdoor</h4>
                <div className="space-y-2">
                  {stateOneData.recreation.map((benefit, index) => (
                    <Badge key={index} variant="outline" className="mr-2 mb-2 border-green-600 text-green-400">
                      {benefit}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-white font-medium mb-2">Other Benefits</h4>
                <div className="space-y-2">
                  {stateOneData.other.map((benefit, index) => (
                    <Badge key={index} variant="outline" className="mr-2 mb-2 border-blue-600 text-blue-400">
                      {benefit}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Savings Calculator */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Estimated Annual Savings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-green-900/20 border border-green-700/30 rounded-lg p-4">
              <h4 className="text-green-300 font-medium mb-2">Tax Savings</h4>
              <p className="text-2xl font-bold text-green-400">${stateOneSavings.tax.toLocaleString()}</p>
              <p className="text-green-200 text-sm">{stateOneSavings.taxDescription}</p>
            </div>
            <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-4">
              <h4 className="text-blue-300 font-medium mb-2">Education Savings</h4>
              <p className="text-2xl font-bold text-blue-400">${stateOneSavings.education.toLocaleString()}</p>
              <p className="text-blue-200 text-sm">{stateOneSavings.educationDescription}</p>
            </div>
            <div className="bg-purple-900/20 border border-purple-700/30 rounded-lg p-4">
              <h4 className="text-purple-300 font-medium mb-2">Other Benefits</h4>
              <p className="text-2xl font-bold text-purple-400">${stateOneSavings.other.toLocaleString()}</p>
              <p className="text-purple-200 text-sm">{stateOneSavings.otherDescription}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Comparison Section */}
      {showComparison && stateTwoData && stateTwoCOL && stateTwoSavings && (
        <Card className="bg-slate-800 border-slate-700 mt-8">
          <CardHeader>
            <CardTitle className="text-white text-2xl">
              Benefit Comparison: {stateOneData.name} vs. {stateTwoData.name}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Benefit Categories Comparison */}
            {(['taxBenefits', 'education', 'vehicle', 'recreation', 'other'] as const).map(categoryKey => {
              const categoryName = categoryKey.replace('Benefits', '').replace(/([A-Z])/g, ' $1').trim();
              const icon = categoryKey === 'taxBenefits' ? <DollarSign className="w-4 h-4 mr-2" /> :
                           categoryKey === 'education' ? <GraduationCap className="w-4 h-4 mr-2" /> :
                           categoryKey === 'vehicle' ? <Car className="w-4 h-4 mr-2" /> :
                           <Heart className="w-4 h-4 mr-2" />; // Default for recreation/other
              
              return (
                <div key={categoryKey}>
                  <h3 className="text-xl text-white font-semibold mb-3 flex items-center capitalize">
                    {icon} {categoryName}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-slate-300 font-medium mb-2">{stateOneData.name}</h4>
                      <ul className="list-disc list-inside space-y-1">
                        {stateOneData[categoryKey].map((benefit, index) => (
                          <li key={`${categoryKey}-1-${index}`} className="text-slate-400 text-sm">{benefit}</li>
                        ))}
                        {stateOneData[categoryKey].length === 0 && <li className="text-slate-500 text-sm italic">No specific benefits listed.</li>}
                      </ul>
                    </div>
                    <div>
                      <h4 className="text-slate-300 font-medium mb-2">{stateTwoData.name}</h4>
                      <ul className="list-disc list-inside space-y-1">
                        {stateTwoData[categoryKey].map((benefit, index) => (
                          <li key={`${categoryKey}-2-${index}`} className="text-slate-400 text-sm">{benefit}</li>
                        ))}
                        {stateTwoData[categoryKey].length === 0 && <li className="text-slate-500 text-sm italic">No specific benefits listed.</li>}
                      </ul>
                    </div>
                  </div>
                </div>
              );
            })}

            {/* Cost of Living Comparison */}
            <div>
              <h3 className="text-xl text-white font-semibold mb-3 flex items-center">
                <Home className="w-5 h-5 mr-2" /> Cost of Living Index Comparison
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-slate-300 font-medium mb-2">{stateOneData.name}</h4>
                  <div className="space-y-3">
                    {Object.entries(stateOneCOL).map(([category, value]) => (
                      <div key={`col-1-${category}`} className="flex items-center justify-between">
                        <span className="text-slate-300 capitalize text-sm">{category}</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-slate-700 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${value > 100 ? "bg-red-400" : value > 95 ? "bg-yellow-400" : "bg-green-400"}`}
                              style={{ width: `${Math.min(value, 120)}%` }}
                            ></div>
                          </div>
                          <span className={`text-xs font-medium ${value > 100 ? "text-red-400" : value > 95 ? "text-yellow-400" : "text-green-400"}`}>{value}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="text-slate-300 font-medium mb-2">{stateTwoData.name}</h4>
                   <div className="space-y-3">
                    {Object.entries(stateTwoCOL).map(([category, value]) => (
                      <div key={`col-2-${category}`} className="flex items-center justify-between">
                        <span className="text-slate-300 capitalize text-sm">{category}</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-slate-700 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${value > 100 ? "bg-red-400" : value > 95 ? "bg-yellow-400" : "bg-green-400"}`}
                              style={{ width: `${Math.min(value, 120)}%` }}
                            ></div>
                          </div>
                          <span className={`text-xs font-medium ${value > 100 ? "text-red-400" : value > 95 ? "text-yellow-400" : "text-green-400"}`}>{value}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
               <div className="mt-4 p-3 bg-slate-700 rounded-lg">
                <p className="text-slate-300 text-sm"><strong>Note:</strong> Index based on national average of 100. Lower numbers indicate lower costs.</p>
              </div>
            </div>

            {/* Estimated Annual Savings Comparison */}
            <div>
              <h3 className="text-xl text-white font-semibold mb-3">Estimated Annual Savings Comparison</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* State One Savings */}
                <div className="space-y-3">
                   <h4 className="text-slate-300 font-medium mb-2">{stateOneData.name}</h4>
                  <div className="bg-green-900/20 border border-green-700/30 rounded-lg p-3">
                    <h5 className="text-green-300 font-medium mb-1 text-sm">Tax Savings</h5>
                    <p className="text-xl font-bold text-green-400">${stateOneSavings.tax.toLocaleString()}</p>
                    <p className="text-green-200 text-xs">{stateOneSavings.taxDescription}</p>
                  </div>
                  <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-3">
                    <h5 className="text-blue-300 font-medium mb-1 text-sm">Education Savings</h5>
                    <p className="text-xl font-bold text-blue-400">${stateOneSavings.education.toLocaleString()}</p>
                    <p className="text-blue-200 text-xs">{stateOneSavings.educationDescription}</p>
                  </div>
                  <div className="bg-purple-900/20 border border-purple-700/30 rounded-lg p-3">
                    <h5 className="text-purple-300 font-medium mb-1 text-sm">Other Benefits</h5>
                    <p className="text-xl font-bold text-purple-400">${stateOneSavings.other.toLocaleString()}</p>
                    <p className="text-purple-200 text-xs">{stateOneSavings.otherDescription}</p>
                  </div>
                </div>
                {/* State Two Savings */}
                <div className="space-y-3">
                  <h4 className="text-slate-300 font-medium mb-2">{stateTwoData.name}</h4>
                  <div className="bg-green-900/20 border border-green-700/30 rounded-lg p-3">
                    <h5 className="text-green-300 font-medium mb-1 text-sm">Tax Savings</h5>
                    <p className="text-xl font-bold text-green-400">${stateTwoSavings.tax.toLocaleString()}</p>
                    <p className="text-green-200 text-xs">{stateTwoSavings.taxDescription}</p>
                  </div>
                  <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-3">
                    <h5 className="text-blue-300 font-medium mb-1 text-sm">Education Savings</h5>
                    <p className="text-xl font-bold text-blue-400">${stateTwoSavings.education.toLocaleString()}</p>
                    <p className="text-blue-200 text-xs">{stateTwoSavings.educationDescription}</p>
                  </div>
                  <div className="bg-purple-900/20 border border-purple-700/30 rounded-lg p-3">
                    <h5 className="text-purple-300 font-medium mb-1 text-sm">Other Benefits</h5>
                    <p className="text-xl font-bold text-purple-400">${stateTwoSavings.other.toLocaleString()}</p>
                    <p className="text-purple-200 text-xs">{stateTwoSavings.otherDescription}</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
