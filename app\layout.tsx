import type { Metadata } from 'next'
import './globals.css'
import { MTCCThemeProvider } from '@/components/mtcc-theme-provider'

export const metadata: Metadata = {
  title: 'Military Transition Command Center • Secure Login',
  description: 'Empowering service members with seamless tools and resources to navigate civilian life with confidence.',
  generator: 'MTCC',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className="font-inter bg-neutral-900 text-neutral-100 min-h-screen">
        <MTCCThemeProvider>
          <div className="min-h-screen bg-neutral-900 text-neutral-100">
            {children}
          </div>
        </MTCCThemeProvider>
      </body>
    </html>
  )
}
