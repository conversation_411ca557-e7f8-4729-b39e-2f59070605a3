"use client"

import { useState, useEffect } from 'react'
import { MTCCClasses, useMTCCTheme } from './mtcc-theme-provider'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { CheckCircle, AlertTriangle, Shield, Users, Calendar, Settings } from 'lucide-react'

export function ThemeVerification() {
  const { theme, classes } = useMTCCTheme()
  const [verificationResults, setVerificationResults] = useState<any[]>([])

  useEffect(() => {
    // Verify theme consistency across the page
    const verifyTheme = () => {
      const results = []

      // Check if body has correct background
      const bodyBg = window.getComputedStyle(document.body).backgroundColor
      results.push({
        test: 'Body Background',
        expected: 'rgb(23, 23, 23)', // neutral-900
        actual: bodyBg,
        passed: bodyBg === 'rgb(23, 23, 23)' || bodyBg.includes('23, 23, 23')
      })

      // Check if body has correct text color
      const bodyColor = window.getComputedStyle(document.body).color
      results.push({
        test: 'Body Text Color',
        expected: 'rgb(245, 245, 245)', // neutral-100
        actual: bodyColor,
        passed: bodyColor === 'rgb(245, 245, 245)' || bodyColor.includes('245, 245, 245')
      })

      // Check if fonts are loaded
      const bodyFont = window.getComputedStyle(document.body).fontFamily
      results.push({
        test: 'Font Family',
        expected: 'Inter',
        actual: bodyFont,
        passed: bodyFont.includes('Inter')
      })

      // Check if CSS variables are set
      const rootStyles = window.getComputedStyle(document.documentElement)
      const primaryColor = rootStyles.getPropertyValue('--mtcc-blue-500')
      results.push({
        test: 'CSS Variables',
        expected: '#3b82f6',
        actual: primaryColor,
        passed: primaryColor.includes('#3b82f6') || primaryColor.includes('59, 130, 246')
      })

      setVerificationResults(results)
    }

    verifyTheme()
  }, [])

  const passedTests = verificationResults.filter(r => r.passed).length
  const totalTests = verificationResults.length
  const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0

  return (
    <div className="min-h-screen bg-neutral-900 text-neutral-100 font-inter p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center mb-8 animate-delayed-1">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-12 h-12 rounded-xl bg-blue-500/10 border border-blue-500/20 flex items-center justify-center">
              <Shield className="w-6 h-6 text-blue-400" />
            </div>
            <h1 className={classes.heading1}>MTCC Theme Verification</h1>
          </div>
          <p className={classes.body}>
            Verify that the unified military theme is applied consistently across all components
          </p>
        </div>

        {/* Theme Status */}
        <Card className="bg-neutral-800 border-neutral-700 animate-delayed-2">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <CheckCircle className="w-5 h-5 mr-2 text-green-400" />
              Theme Consistency Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold font-montserrat text-blue-400 mb-2">
                  {Math.round(successRate)}%
                </div>
                <div className="text-neutral-400 text-sm">Success Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold font-montserrat text-green-400 mb-2">
                  {passedTests}
                </div>
                <div className="text-neutral-400 text-sm">Tests Passed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold font-montserrat text-neutral-300 mb-2">
                  {totalTests}
                </div>
                <div className="text-neutral-400 text-sm">Total Tests</div>
              </div>
            </div>
            <div className="mt-6">
              <Progress value={successRate} className="h-3" />
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card className="bg-neutral-800 border-neutral-700 animate-delayed-3">
          <CardHeader>
            <CardTitle className="text-white">Verification Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {verificationResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-neutral-700/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {result.passed ? (
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    ) : (
                      <AlertTriangle className="w-5 h-5 text-red-400" />
                    )}
                    <div>
                      <div className="text-white font-medium">{result.test}</div>
                      <div className="text-neutral-400 text-sm">
                        Expected: {result.expected}
                      </div>
                      <div className="text-neutral-400 text-sm">
                        Actual: {result.actual}
                      </div>
                    </div>
                  </div>
                  <Badge className={result.passed ? 'status-active' : 'status-danger'}>
                    {result.passed ? 'PASS' : 'FAIL'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Component Showcase */}
        <Card className="bg-neutral-800 border-neutral-700 animate-delayed-4">
          <CardHeader>
            <CardTitle className="text-white">Component Theme Showcase</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Buttons */}
              <div className="space-y-3">
                <h3 className={classes.heading3}>Buttons</h3>
                <div className="space-y-2">
                  <Button className="btn-primary w-full">Primary Button</Button>
                  <Button className="btn-secondary w-full">Secondary Button</Button>
                  <Button className="glass-button w-full">Glass Button</Button>
                </div>
              </div>

              {/* Status Badges */}
              <div className="space-y-3">
                <h3 className={classes.heading3}>Status Badges</h3>
                <div className="space-y-2">
                  <Badge className="status-active">Active</Badge>
                  <Badge className="status-warning">Warning</Badge>
                  <Badge className="status-danger">Danger</Badge>
                  <Badge className="status-info">Info</Badge>
                </div>
              </div>

              {/* Form Elements */}
              <div className="space-y-3">
                <h3 className={classes.heading3}>Form Elements</h3>
                <div className="space-y-2">
                  <Input className="form-input" placeholder="MTCC themed input" />
                  <div className="progress-bar h-3">
                    <div className="progress-fill progress-75"></div>
                  </div>
                </div>
              </div>

              {/* Navigation Links */}
              <div className="space-y-3">
                <h3 className={classes.heading3}>Navigation</h3>
                <div className="space-y-2">
                  <div className="nav-link active flex items-center space-x-2">
                    <Users className="w-4 h-4" />
                    <span>Active Link</span>
                  </div>
                  <div className="nav-link flex items-center space-x-2">
                    <Calendar className="w-4 h-4" />
                    <span>Inactive Link</span>
                  </div>
                </div>
              </div>

              {/* Cards */}
              <div className="space-y-3">
                <h3 className={classes.heading3}>Cards</h3>
                <div className="card-primary p-4">
                  <div className="text-white font-medium mb-2">MTCC Card</div>
                  <div className="text-neutral-400 text-sm">
                    This card uses the unified MTCC theme
                  </div>
                </div>
              </div>

              {/* Typography */}
              <div className="space-y-3">
                <h3 className={classes.heading3}>Typography</h3>
                <div className="space-y-2">
                  <div className="font-montserrat font-semibold text-white">Montserrat Heading</div>
                  <div className="font-inter text-neutral-300">Inter Body Text</div>
                  <div className="font-inter text-neutral-400 text-sm">Secondary Text</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Test */}
        <Card className="bg-neutral-800 border-neutral-700 animate-delayed-5">
          <CardHeader>
            <CardTitle className="text-white">Navigation Pages Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button 
                onClick={() => window.location.href = '/'}
                className="btn-secondary flex items-center space-x-2"
              >
                <Shield className="w-4 h-4" />
                <span>Main App</span>
              </Button>
              <Button 
                onClick={() => window.location.href = '/family-demo'}
                className="btn-secondary flex items-center space-x-2"
              >
                <Users className="w-4 h-4" />
                <span>Family Demo</span>
              </Button>
              <Button 
                onClick={() => window.location.href = '/questionnaire-demo'}
                className="btn-secondary flex items-center space-x-2"
              >
                <Calendar className="w-4 h-4" />
                <span>Questionnaire</span>
              </Button>
              <Button 
                onClick={() => window.location.href = '/theme-verification'}
                className="btn-primary flex items-center space-x-2"
              >
                <Settings className="w-4 h-4" />
                <span>This Page</span>
              </Button>
            </div>
            <div className="mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <div className="text-blue-300 text-sm">
                <strong>Instructions:</strong> Click each button to navigate to different pages and verify that:
                <ul className="mt-2 space-y-1 list-disc list-inside">
                  <li>Background is always neutral-900 (dark charcoal)</li>
                  <li>Text is always neutral-100 (light gray)</li>
                  <li>Cards use neutral-800 background</li>
                  <li>Buttons use blue-500 primary color</li>
                  <li>Fonts are Inter (body) and Montserrat (headings)</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
