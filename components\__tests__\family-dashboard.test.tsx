import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { FamilyDashboard } from '../family-dashboard'

// Mock the UI components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardContent: ({ children }: any) => <div>{children}</div>,
  CardHeader: ({ children }: any) => <div>{children}</div>,
  CardTitle: ({ children }: any) => <h3>{children}</h3>,
}))

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} {...props}>
      {children}
    </button>
  ),
}))

jest.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div>{children}</div>,
  DialogHeader: ({ children }: any) => <div>{children}</div>,
  DialogTitle: ({ children }: any) => <h2>{children}</h2>,
  DialogTrigger: ({ children }: any) => <div>{children}</div>,
}))

jest.mock('@/components/ui/input', () => ({
  Input: ({ value, onChange, ...props }: any) => (
    <input value={value} onChange={onChange} {...props} />
  ),
}))

jest.mock('@/components/ui/select', () => ({
  Select: ({ children, onValueChange }: any) => <div onClick={() => onValueChange('test')}>{children}</div>,
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => <option value={value}>{children}</option>,
  SelectTrigger: ({ children }: any) => <div role="combobox" aria-label="Select option" aria-expanded="false" aria-controls="select-content">{children}</div>,
  SelectValue: ({ placeholder }: any) => <span title={placeholder}>{placeholder}</span>,
}))

jest.mock('@/components/ui/textarea', () => ({
  Textarea: ({ value, onChange, ...props }: any) => (
    <textarea value={value} onChange={onChange} {...props} />
  ),
}))

jest.mock('@/components/ui/label', () => ({
  Label: ({ children }: any) => <label>{children}</label>,
}))

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children }: any) => <span>{children}</span>,
}))

jest.mock('@/components/ui/progress', () => ({
  Progress: ({ value }: any) => <div data-testid="progress" data-value={value}></div>,
}))

describe('FamilyDashboard', () => {
  test('renders family dashboard with initial family members', () => {
    render(<FamilyDashboard />)
    
    expect(screen.getByText('Family Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Sarah Johnson')).toBeInTheDocument()
    expect(screen.getByText('Michael Johnson')).toBeInTheDocument()
    expect(screen.getByText('Emma Johnson')).toBeInTheDocument()
  })

  test('opens add family member dialog when button is clicked', async () => {
    render(<FamilyDashboard />)
    
    const addButton = screen.getByText('Add Family Member')
    fireEvent.click(addButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
      expect(screen.getByText('Add Family Member')).toBeInTheDocument()
    })
  })

  test('adds a new family member when form is submitted', async () => {
    render(<FamilyDashboard />)
    
    // Open dialog
    const addButton = screen.getByText('Add Family Member')
    fireEvent.click(addButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
    })
    
    // Fill form
    const nameInput = screen.getByPlaceholderText('Enter full name')
    fireEvent.change(nameInput, { target: { value: 'John Doe' } })
    
    const concernsTextarea = screen.getByPlaceholderText('Enter concerns separated by commas')
    fireEvent.change(concernsTextarea, { target: { value: 'Test concern 1, Test concern 2' } })
    
    // Submit form
    const submitButton = screen.getByText('Add Member')
    fireEvent.click(submitButton)
    
    // Check if new member appears (this would need more sophisticated mocking for full test)
    expect(nameInput.value).toBe('John Doe')
  })

  test('validates required fields', async () => {
    render(<FamilyDashboard />)
    
    // Open dialog
    const addButton = screen.getByText('Add Family Member')
    fireEvent.click(addButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
    })
    
    // Try to submit without required fields
    const submitButton = screen.getByText('Add Member')
    expect(submitButton).toBeDisabled()
  })
})
