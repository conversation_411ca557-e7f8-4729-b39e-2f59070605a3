@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap');

/* MTCC UNIFIED THEME SYSTEM - OVERRIDES ALL OTHER THEMES */
:root {
  /* MTCC Military Color Palette - Based on Login Page */
  --mtcc-neutral-900: #171717;
  --mtcc-neutral-800: #262626;
  --mtcc-neutral-700: #404040;
  --mtcc-neutral-600: #525252;
  --mtcc-neutral-500: #737373;
  --mtcc-neutral-400: #a3a3a3;
  --mtcc-neutral-300: #d4d4d4;
  --mtcc-neutral-200: #e5e5e5;
  --mtcc-neutral-100: #f5f5f5;

  --mtcc-blue-500: #3b82f6;
  --mtcc-blue-400: #60a5fa;
  --mtcc-blue-600: #2563eb;
  --mtcc-blue-300: #93c5fd;

  --mtcc-red-500: #ef4444;
  --mtcc-red-400: #f87171;
  --mtcc-red-600: #dc2626;

  --mtcc-green-500: #10b981;
  --mtcc-green-400: #34d399;
  --mtcc-green-600: #059669;

  --mtcc-yellow-500: #f59e0b;
  --mtcc-yellow-400: #fbbf24;
  --mtcc-yellow-300: #fcd34d;

  /* Override ShadCN Variables with MTCC Theme */
  --background: 23 23 23; /* neutral-900 */
  --foreground: 245 245 245; /* neutral-100 */
  --card: 38 38 38; /* neutral-800 */
  --card-foreground: 245 245 245; /* neutral-100 */
  --popover: 38 38 38; /* neutral-800 */
  --popover-foreground: 245 245 245; /* neutral-100 */
  --primary: 59 130 246; /* blue-500 */
  --primary-foreground: 245 245 245; /* neutral-100 */
  --secondary: 64 64 64; /* neutral-700 */
  --secondary-foreground: 245 245 245; /* neutral-100 */
  --muted: 64 64 64; /* neutral-700 */
  --muted-foreground: 163 163 163; /* neutral-400 */
  --accent: 59 130 246; /* blue-500 */
  --accent-foreground: 245 245 245; /* neutral-100 */
  --destructive: 239 68 68; /* red-500 */
  --destructive-foreground: 245 245 245; /* neutral-100 */
  --border: 64 64 64; /* neutral-700 */
  --input: 64 64 64; /* neutral-700 */
  --ring: 59 130 246; /* blue-500 */
  --radius: 0.5rem;

  /* Sidebar Variables */
  --sidebar-background: 23 23 23; /* neutral-900 */
  --sidebar-foreground: 245 245 245; /* neutral-100 */
  --sidebar-primary: 59 130 246; /* blue-500 */
  --sidebar-primary-foreground: 245 245 245; /* neutral-100 */
  --sidebar-accent: 38 38 38; /* neutral-800 */
  --sidebar-accent-foreground: 245 245 245; /* neutral-100 */
  --sidebar-border: 64 64 64; /* neutral-700 */
  --sidebar-ring: 59 130 246; /* blue-500 */
}

/* Force MTCC Theme on Body and HTML */
html, body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  background-color: var(--mtcc-neutral-900) !important;
  color: var(--mtcc-neutral-100) !important;
  min-height: 100vh;
}

/* Force Dark Mode - Override Light Mode */
.dark {
  --background: 23 23 23; /* neutral-900 */
  --foreground: 245 245 245; /* neutral-100 */
  --card: 38 38 38; /* neutral-800 */
  --card-foreground: 245 245 245; /* neutral-100 */
  --popover: 38 38 38; /* neutral-800 */
  --popover-foreground: 245 245 245; /* neutral-100 */
  --primary: 59 130 246; /* blue-500 */
  --primary-foreground: 245 245 245; /* neutral-100 */
  --secondary: 64 64 64; /* neutral-700 */
  --secondary-foreground: 245 245 245; /* neutral-100 */
  --muted: 64 64 64; /* neutral-700 */
  --muted-foreground: 163 163 163; /* neutral-400 */
  --accent: 59 130 246; /* blue-500 */
  --accent-foreground: 245 245 245; /* neutral-100 */
  --destructive: 239 68 68; /* red-500 */
  --destructive-foreground: 245 245 245; /* neutral-100 */
  --border: 64 64 64; /* neutral-700 */
  --input: 64 64 64; /* neutral-700 */
  --ring: 59 130 246; /* blue-500 */
  --sidebar-background: 23 23 23; /* neutral-900 */
  --sidebar-foreground: 245 245 245; /* neutral-100 */
  --sidebar-primary: 59 130 246; /* blue-500 */
  --sidebar-primary-foreground: 245 245 245; /* neutral-100 */
  --sidebar-accent: 38 38 38; /* neutral-800 */
  --sidebar-accent-foreground: 245 245 245; /* neutral-100 */
  --sidebar-border: 64 64 64; /* neutral-700 */
  --sidebar-ring: 59 130 246; /* blue-500 */
}

/* MTCC Typography Classes */
.font-inter {
  font-family: 'Inter', sans-serif !important;
}

.font-montserrat {
  font-family: 'Montserrat', sans-serif !important;
}

/* Military Theme Animations */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Animation Classes */
.animate-delayed-1 { animation: fadeInUp 0.8s 0.1s both; }
.animate-delayed-2 { animation: fadeInUp 0.8s 0.25s both; }
.animate-delayed-3 { animation: fadeInUp 0.8s 0.4s both; }
.animate-delayed-4 { animation: fadeInUp 0.8s 0.55s both; }
.animate-delayed-5 { animation: fadeInUp 0.8s 0.7s both; }
.animate-slide-left { animation: slideInLeft 0.6s ease-out; }
.animate-pulse-slow { animation: pulse 2s infinite; }

/* Glass Morphism Effects */
.glass-card {
  background: rgba(38, 38, 38, 0.8);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(64, 64, 64, 0.3);
  border-radius: 12px;
}

.glass-sidebar {
  background: rgba(23, 23, 23, 0.9);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(64, 64, 64, 0.3);
}

.glass-nav {
  background: rgba(23, 23, 23, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(64, 64, 64, 0.3);
}

.glass-button {
  background: rgba(59, 130, 246, 0.1);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
}

/* MTCC Button Styles */
.btn-primary {
  background: linear-gradient(135deg, var(--mtcc-blue-500), var(--mtcc-blue-600)) !important;
  color: var(--mtcc-neutral-100) !important;
  border: none !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3) !important;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--mtcc-blue-600), #1d4ed8) !important;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4) !important;
  transform: translateY(-1px) !important;
}

.btn-secondary {
  background: rgba(64, 64, 64, 0.8) !important;
  color: var(--mtcc-neutral-200) !important;
  border: 1px solid rgba(115, 115, 115, 0.3) !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.btn-secondary:hover {
  background: rgba(82, 82, 82, 0.9) !important;
  border-color: rgba(115, 115, 115, 0.5) !important;
  transform: translateY(-1px) !important;
}

/* MTCC Card Styles */
.card-primary {
  background: rgba(38, 38, 38, 0.8) !important;
  border: 1px solid rgba(64, 64, 64, 0.3) !important;
  -webkit-backdrop-filter: blur(16px) !important;
  backdrop-filter: blur(16px) !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
}

.card-primary:hover {
  border-color: rgba(59, 130, 246, 0.3) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  transform: translateY(-2px) !important;
}

/* MTCC Form Styles */
.form-input {
  background: rgba(64, 64, 64, 0.6) !important;
  border: 1px solid rgba(115, 115, 115, 0.3) !important;
  color: var(--mtcc-neutral-200) !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  transition: all 0.3s ease !important;
}

.form-input:focus {
  outline: none !important;
  border-color: var(--mtcc-blue-500) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background: rgba(64, 64, 64, 0.8) !important;
}

.form-input::placeholder {
  color: var(--mtcc-neutral-500) !important;
}

/* MTCC Status Indicators */
.status-active {
  background: linear-gradient(135deg, var(--mtcc-green-500), var(--mtcc-green-600)) !important;
  color: white !important;
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3) !important;
}

.status-warning {
  background: linear-gradient(135deg, var(--mtcc-yellow-500), #d97706) !important;
  color: white !important;
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.3) !important;
}

.status-danger {
  background: linear-gradient(135deg, var(--mtcc-red-500), var(--mtcc-red-600)) !important;
  color: white !important;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3) !important;
}

.status-info {
  background: linear-gradient(135deg, var(--mtcc-blue-500), var(--mtcc-blue-600)) !important;
  color: white !important;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3) !important;
}

/* MTCC Progress Bars */
.progress-bar {
  background: rgba(64, 64, 64, 0.3) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

.progress-fill {
  background: linear-gradient(90deg, var(--mtcc-blue-500), var(--mtcc-blue-600)) !important;
  height: 100% !important;
  border-radius: 8px !important;
  transition: width 0.6s ease !important;
}

/* MTCC Navigation Styles */
.nav-link {
  color: var(--mtcc-neutral-400) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
}

.nav-link:hover {
  color: var(--mtcc-blue-400) !important;
}

.nav-link.active {
  color: var(--mtcc-blue-400) !important;
}

.nav-link.active::after {
  content: '' !important;
  position: absolute !important;
  bottom: -4px !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, var(--mtcc-blue-500), var(--mtcc-blue-600)) !important;
  border-radius: 1px !important;
}

/* MTCC Utility Classes */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

.border-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.2) !important;
}

.hover-lift {
  transition: transform 0.3s ease !important;
}

.hover-lift:hover {
  transform: translateY(-4px) !important;
}

/* Military Theme Animations */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Animation Delay Classes */
.animate-delayed-1 { animation: fadeInUp 0.8s 0.1s both; }
.animate-delayed-2 { animation: fadeInUp 0.8s 0.25s both; }
.animate-delayed-3 { animation: fadeInUp 0.8s 0.4s both; }
.animate-delayed-4 { animation: fadeInUp 0.8s 0.55s both; }
.animate-delayed-5 { animation: fadeInUp 0.8s 0.7s both; }
.animate-slide-left { animation: slideInLeft 0.6s ease-out; }

/* Glass Morphism Effects */
.glass-card {
  background: rgba(38, 38, 38, 0.8);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(64, 64, 64, 0.3);
  border-radius: 12px;
}

.glass-sidebar {
  background: rgba(23, 23, 23, 0.9);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(64, 64, 64, 0.3);
}

.glass-button {
  background: rgba(59, 130, 246, 0.1);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
}

/* Military Badge Styles */
.military-badge {
  background: linear-gradient(135deg, var(--military-primary), var(--military-secondary));
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.military-badge-danger {
  background: linear-gradient(135deg, var(--military-accent), #dc2626);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--military-neutral-800);
}

::-webkit-scrollbar-thumb {
  background: var(--military-neutral-600);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--military-neutral-500);
}

/* Toggle Switch Styles */
.toggle {
  width: 2.75rem;
  height: 1.5rem;
  background: var(--military-neutral-600);
  border-radius: 9999px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.toggle.checked {
  background: var(--military-primary);
}

.toggle-dot {
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  background: white;
  border-radius: 50%;
  transition: transform 0.25s ease;
}

.toggle.checked .toggle-dot {
  transform: translateX(1.25rem);
}

/* Focus States */
.focus-military:focus {
  outline: none;
  ring: 2px;
  ring-color: var(--military-primary);
  ring-opacity: 0.5;
}

@layer utilities {
  .text-balance {
    /* Fallback for older browsers */
    word-wrap: break-word;
    /* Modern browsers */
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

/* FORCE MTCC THEME ON ALL COMPONENTS */
@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    background-color: var(--mtcc-neutral-900) !important;
    color: var(--mtcc-neutral-100) !important;
    font-family: 'Inter', sans-serif !important;
  }

  /* Force MTCC Theme on All Cards */
  .card, [data-card] {
    background-color: var(--mtcc-neutral-800) !important;
    border-color: var(--mtcc-neutral-700) !important;
    color: var(--mtcc-neutral-100) !important;
  }

  /* Force MTCC Theme on All Buttons */
  .btn, button[class*="bg-"] {
    font-family: 'Inter', sans-serif !important;
  }

  /* Force MTCC Theme on All Inputs */
  input, textarea, select {
    background-color: var(--mtcc-neutral-700) !important;
    border-color: var(--mtcc-neutral-600) !important;
    color: var(--mtcc-neutral-100) !important;
    font-family: 'Inter', sans-serif !important;
  }

  input:focus, textarea:focus, select:focus {
    border-color: var(--mtcc-blue-500) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }

  /* Force MTCC Theme on All Dialogs */
  [role="dialog"], .dialog-content {
    background-color: var(--mtcc-neutral-800) !important;
    border-color: var(--mtcc-neutral-700) !important;
    color: var(--mtcc-neutral-100) !important;
  }

  /* Force MTCC Theme on All Badges */
  .badge {
    font-family: 'Inter', sans-serif !important;
  }

  /* Force MTCC Theme on All Progress Bars */
  .progress {
    background-color: var(--mtcc-neutral-700) !important;
  }

  /* Force MTCC Theme on All Sidebars */
  .sidebar, [data-sidebar] {
    background-color: var(--mtcc-neutral-900) !important;
    border-color: var(--mtcc-neutral-700) !important;
    color: var(--mtcc-neutral-100) !important;
  }

  /* Force MTCC Theme on All Navigation */
  nav, .nav {
    background-color: var(--mtcc-neutral-900) !important;
    color: var(--mtcc-neutral-100) !important;
  }

  /* Force MTCC Theme on All Headers */
  h1, h2, h3, h4, h5, h6 {
    color: var(--mtcc-neutral-100) !important;
  }

  /* Force MTCC Theme on All Text */
  p, span, div {
    color: inherit !important;
  }
}

/* AGGRESSIVE THEME OVERRIDE - FORCE MTCC THEME ON EVERYTHING */
* {
  box-sizing: border-box !important;
}

/* Override any component that tries to use light theme */
.bg-white, .bg-slate-50, .bg-gray-50, .bg-neutral-50 {
  background-color: var(--mtcc-neutral-800) !important;
}

.text-black, .text-slate-900, .text-gray-900, .text-neutral-900 {
  color: var(--mtcc-neutral-100) !important;
}

.bg-slate-100, .bg-gray-100, .bg-neutral-100 {
  background-color: var(--mtcc-neutral-700) !important;
}

.bg-slate-200, .bg-gray-200, .bg-neutral-200 {
  background-color: var(--mtcc-neutral-600) !important;
}

.bg-slate-800, .bg-gray-800 {
  background-color: var(--mtcc-neutral-800) !important;
}

.bg-slate-900, .bg-gray-900 {
  background-color: var(--mtcc-neutral-900) !important;
}

.text-slate-100, .text-gray-100 {
  color: var(--mtcc-neutral-100) !important;
}

.text-slate-300, .text-gray-300 {
  color: var(--mtcc-neutral-300) !important;
}

.text-slate-400, .text-gray-400 {
  color: var(--mtcc-neutral-400) !important;
}

.border-slate-700, .border-gray-700 {
  border-color: var(--mtcc-neutral-700) !important;
}

.border-slate-600, .border-gray-600 {
  border-color: var(--mtcc-neutral-600) !important;
}

/* Force all ShadCN components to use MTCC theme */
[data-radix-popper-content-wrapper] {
  background-color: var(--mtcc-neutral-800) !important;
  border-color: var(--mtcc-neutral-700) !important;
  color: var(--mtcc-neutral-100) !important;
}

[role="menuitem"], [role="option"] {
  background-color: transparent !important;
  color: var(--mtcc-neutral-100) !important;
}

[role="menuitem"]:hover, [role="option"]:hover {
  background-color: var(--mtcc-neutral-700) !important;
}

/* Force all select dropdowns */
[data-radix-select-content] {
  background-color: var(--mtcc-neutral-800) !important;
  border-color: var(--mtcc-neutral-700) !important;
  color: var(--mtcc-neutral-100) !important;
}

/* Force all dialog content */
[data-radix-dialog-content] {
  background-color: var(--mtcc-neutral-800) !important;
  border-color: var(--mtcc-neutral-700) !important;
  color: var(--mtcc-neutral-100) !important;
}

/* Force all popover content */
[data-radix-popover-content] {
  background-color: var(--mtcc-neutral-800) !important;
  border-color: var(--mtcc-neutral-700) !important;
  color: var(--mtcc-neutral-100) !important;
}

/* Force all tooltip content */
[data-radix-tooltip-content] {
  background-color: var(--mtcc-neutral-800) !important;
  border-color: var(--mtcc-neutral-700) !important;
  color: var(--mtcc-neutral-100) !important;
}

/* FINAL OVERRIDE - ENSURE NO LIGHT THEME ELEMENTS */
@media (prefers-color-scheme: light) {
  :root {
    color-scheme: dark !important;
  }
}

/* Force dark mode on all elements */
html {
  color-scheme: dark !important;
}

/* Override any remaining light theme elements */
.light, [data-theme="light"] {
  background-color: var(--mtcc-neutral-900) !important;
  color: var(--mtcc-neutral-100) !important;
}

/* Progress bar styles to replace inline styles */
.progress-fill {
  transition: width 0.5s ease-in-out;
}

/* Dynamic progress width classes */
.progress-0 { width: 0% !important; }
.progress-10 { width: 10% !important; }
.progress-20 { width: 20% !important; }
.progress-25 { width: 25% !important; }
.progress-30 { width: 30% !important; }
.progress-40 { width: 40% !important; }
.progress-50 { width: 50% !important; }
.progress-60 { width: 60% !important; }
.progress-70 { width: 70% !important; }
.progress-75 { width: 75% !important; }
.progress-80 { width: 80% !important; }
.progress-90 { width: 90% !important; }
.progress-100 { width: 100% !important; }

/* Animation delay classes to replace inline styles */
.delay-0 { animation-delay: 0s !important; }
.delay-100 { animation-delay: 0.1s !important; }
.delay-200 { animation-delay: 0.2s !important; }
.delay-300 { animation-delay: 0.3s !important; }
.delay-400 { animation-delay: 0.4s !important; }
.delay-500 { animation-delay: 0.5s !important; }
.delay-600 { animation-delay: 0.6s !important; }
.delay-700 { animation-delay: 0.7s !important; }
.delay-800 { animation-delay: 0.8s !important; }
.delay-900 { animation-delay: 0.9s !important; }
.delay-1000 { animation-delay: 1s !important; }

/* Cost of living progress widths */
.col-width-80 { width: 80% !important; }
.col-width-85 { width: 85% !important; }
.col-width-95 { width: 95% !important; }
.col-width-105 { width: 105% !important; }
.col-width-110 { width: 110% !important; }
.col-width-115 { width: 115% !important; }
.col-width-120 { width: 120% !important; }
