"use client"

import { useState } from "react"
import { Sidebar } from "./components/sidebar"
import { Shield, Calendar, Target, CheckCircle, Clock, TrendingUp } from "lucide-react"
import { AICoach } from "./components/ai-coach"
import { FamilyDashboard } from "./components/family-dashboard-simple"
import { StateBenefits } from "./components/state-benefits"
import { PeerMentorship } from "./components/peer-mentorship"
import { VATracker } from "./components/va-tracker"
import { FinancialCalculator } from "./components/financial-calculator"
import { JobMatching } from "./components/job-matching"
import { MobileApp } from "./components/mobile-app"
import { ComprehensiveDashboard } from "./components/comprehensive-dashboard"
import { MissionControl } from "./components/mission-control"
import { RetirementTimelinePage } from "./components/retirement-timeline-page"

export default function RetirementDashboard({ user }: { user: any }) {
  const [activeSection, setActiveSection] = useState("dashboard")

  const renderContent = () => {
    switch (activeSection) {
      case "mission-control":
        return <MissionControl user={user} />
      case "ai-coach":
        return <AICoach user={user} />
      case "family-dashboard":
        return <FamilyDashboard />
      case "state-benefits":
        return <StateBenefits user={user} />
      case "peer-mentorship":
        return <PeerMentorship user={user} />
      case "va-tracker":
        return <VATracker user={user} />
      case "financial-calculator":
        return <FinancialCalculator user={user} />
      case "job-matching":
        return <JobMatching user={user} />
      case "mobile-app":
        return <MobileApp user={user} />
      case "timeline":
        return <RetirementTimelinePage user={user} />
      default:
        return <ComprehensiveDashboard user={user} />
    }
  }

  return (
    <>
      {/* Stars Background */}
      <div id="stars-container" className="fixed inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-neutral-900 via-neutral-900 to-neutral-800"></div>
      </div>

      <div className="flex h-screen bg-transparent relative">
        <Sidebar activeSection={activeSection} onSectionChange={setActiveSection} user={user} />

        <div className="flex-1 overflow-auto">
          {/* Military Command Header */}
          <div className="glass-card m-4 mb-0 animate-delayed-1">
            <div className="bg-gradient-to-r from-blue-600/20 via-blue-500/10 to-purple-600/20 p-6 text-white border-b border-neutral-700/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                    <Shield className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold font-montserrat">
                      Welcome back, {user?.rank} {user?.lastName || "Service Member"}
                    </h1>
                    <p className="text-neutral-300 flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>
                        {user?.daysUntilSeparation
                          ? `${user.daysUntilSeparation} days until separation`
                          : "Your transition command center"}
                      </span>
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold font-montserrat text-blue-400">
                        {user?.completionPercentage || 0}%
                      </div>
                      <div className="text-neutral-400 text-sm">Mission Progress</div>
                    </div>
                    <div className="w-16 h-16 relative">
                      <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="rgba(64, 64, 64, 0.3)"
                          strokeWidth="2"
                        />
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#3b82f6"
                          strokeWidth="2"
                          strokeDasharray={`${user?.completionPercentage || 0}, 100`}
                          strokeLinecap="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="glass-button p-3 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Target className="w-4 h-4 text-blue-400" />
                    <div>
                      <div className="text-sm text-neutral-400">Active Goals</div>
                      <div className="font-semibold">12</div>
                    </div>
                  </div>
                </div>
                <div className="glass-button p-3 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <div>
                      <div className="text-sm text-neutral-400">Completed</div>
                      <div className="font-semibold">8</div>
                    </div>
                  </div>
                </div>
                <div className="glass-button p-3 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-yellow-400" />
                    <div>
                      <div className="text-sm text-neutral-400">Pending</div>
                      <div className="font-semibold">4</div>
                    </div>
                  </div>
                </div>
                <div className="glass-button p-3 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-purple-400" />
                    <div>
                      <div className="text-sm text-neutral-400">This Week</div>
                      <div className="font-semibold">+3</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="p-4 pt-2">
            <div className="glass-card p-6 animate-delayed-2">
              {renderContent()}
            </div>
          </div>
        </div>
      </div>

      {/* Initialize Stars Effect */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            if (typeof window !== 'undefined') {
              const createStars = () => {
                const starsContainer = document.getElementById('stars-container');
                if (!starsContainer || starsContainer.children.length > 0) return;

                for (let i = 0; i < 100; i++) {
                  const star = document.createElement('div');
                  star.className = 'absolute w-1 h-1 bg-white rounded-full opacity-30';
                  star.style.left = Math.random() * 100 + '%';
                  star.style.top = Math.random() * 100 + '%';
                  star.style.animationDelay = Math.random() * 3 + 's';
                  star.style.animation = 'twinkle 3s infinite';
                  starsContainer.appendChild(star);
                }
              };

              setTimeout(createStars, 100);
            }
          `,
        }}
      />
    </>
  )
}
