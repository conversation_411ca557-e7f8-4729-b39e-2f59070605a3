"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Heart, Users, GraduationCap, Calendar, AlertTriangle, CheckCircle, Plus, X, Clock, Edit } from "lucide-react"

interface FamilyMember {
  id: string
  name: string
  relationship: string
  age?: number
  status: string
  tasks: number
  completedTasks: number
  concerns: string[]
}

interface FamilyTask {
  id: string
  title: string
  assignedTo: string
  dueDate: string
  status: "completed" | "in-progress" | "not-started"
  priority: "high" | "medium" | "low"
}

interface CalendarEvent {
  id: string
  title: string
  date: string
  time: string
  attendees: string
  category: "school" | "medical" | "social" | "work" | "other"
}

// Storage keys for localStorage
const STORAGE_KEYS = {
  FAMILY_MEMBERS: 'mtcc_family_members',
  FAMILY_TASKS: 'mtcc_family_tasks',
  CALENDAR_EVENTS: 'mtcc_calendar_events'
}

// Default data
const DEFAULT_FAMILY_MEMBERS: FamilyMember[] = [
  {
    id: "1",
    name: "Sarah Johnson",
    relationship: "Spouse",
    status: "Active",
    tasks: 3,
    completedTasks: 2,
    concerns: ["Healthcare transition", "Job search support"],
  },
  {
    id: "2",
    name: "Michael Johnson",
    relationship: "Son",
    age: 16,
    status: "Student",
    tasks: 2,
    completedTasks: 1,
    concerns: ["School transfer", "College planning"],
  },
  {
    id: "3",
    name: "Emma Johnson",
    relationship: "Daughter",
    age: 12,
    status: "Student",
    tasks: 1,
    completedTasks: 1,
    concerns: ["New school adjustment"],
  },
]

// Storage utility functions
const saveToStorage = (key: string, data: any) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (error) {
    console.error('Error saving to localStorage:', error)
  }
}

const loadFromStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const saved = localStorage.getItem(key)
    if (saved) {
      return JSON.parse(saved)
    }
  } catch (error) {
    console.error('Error loading from localStorage:', error)
  }
  return defaultValue
}

export function FamilyDashboard() {
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([])
  const [isLoaded, setIsLoaded] = useState(false)
  const [familyTasks, setFamilyTasks] = useState<FamilyTask[]>([])
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([])

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isAddTaskDialogOpen, setIsAddTaskDialogOpen] = useState(false)
  const [isAddEventDialogOpen, setIsAddEventDialogOpen] = useState(false)
  
  const [newMember, setNewMember] = useState({
    name: "",
    relationship: "",
    age: "",
    status: "",
    concerns: ""
  })

  const [newTask, setNewTask] = useState({
    title: "",
    assignedTo: "",
    dueDate: "",
    priority: "medium" as "high" | "medium" | "low"
  })

  const [newEvent, setNewEvent] = useState({
    title: "",
    date: "",
    time: "",
    attendees: "",
    category: "other" as "school" | "medical" | "social" | "work" | "other"
  })

  // Default data for tasks and events
  const DEFAULT_FAMILY_TASKS: FamilyTask[] = [
    {
      id: "1",
      title: "Update DEERS Information",
      assignedTo: "Spouse",
      dueDate: "Jul 15, 2024",
      status: "completed",
      priority: "high",
    },
    {
      id: "2",
      title: "Research School Districts",
      assignedTo: "Both Parents",
      dueDate: "Jul 20, 2024",
      status: "in-progress",
      priority: "high",
    },
    {
      id: "3",
      title: "Schedule Family Counseling",
      assignedTo: "Service Member",
      dueDate: "Jul 25, 2024",
      status: "not-started",
      priority: "medium",
    },
    {
      id: "4",
      title: "Plan Farewell Activities",
      assignedTo: "Family",
      dueDate: "Aug 1, 2024",
      status: "not-started",
      priority: "low",
    },
  ]

  const DEFAULT_CALENDAR_EVENTS: CalendarEvent[] = [
    {
      id: "1",
      title: "School Visit",
      date: "Jul 18, 2024",
      time: "2:00 PM",
      attendees: "Michael & Emma",
      category: "school"
    },
    {
      id: "2",
      title: "Family Counseling",
      date: "Jul 22, 2024",
      time: "10:00 AM",
      attendees: "Whole Family",
      category: "medical"
    },
    {
      id: "3",
      title: "House Hunting",
      date: "Jul 25, 2024",
      time: "9:00 AM",
      attendees: "Parents",
      category: "other"
    },
    {
      id: "4",
      title: "Farewell Party",
      date: "Aug 5, 2024",
      time: "6:00 PM",
      attendees: "Whole Family",
      category: "social"
    }
  ]

  // Load data from localStorage on component mount
  useEffect(() => {
    const loadedMembers = loadFromStorage(STORAGE_KEYS.FAMILY_MEMBERS, DEFAULT_FAMILY_MEMBERS)
    const loadedTasks = loadFromStorage(STORAGE_KEYS.FAMILY_TASKS, DEFAULT_FAMILY_TASKS)
    const loadedEvents = loadFromStorage(STORAGE_KEYS.CALENDAR_EVENTS, DEFAULT_CALENDAR_EVENTS)
    
    setFamilyMembers(loadedMembers)
    setFamilyTasks(loadedTasks)
    setCalendarEvents(loadedEvents)
    setIsLoaded(true)
  }, [])

  const handleAddMember = () => {
    if (!newMember.name.trim() || !newMember.relationship.trim()) {
      return // Basic validation
    }

    const member: FamilyMember = {
      id: Date.now().toString(),
      name: newMember.name.trim(),
      relationship: newMember.relationship,
      age: newMember.age ? parseInt(newMember.age) : undefined,
      status: newMember.status || "Active",
      tasks: 0,
      completedTasks: 0,
      concerns: newMember.concerns
        ? newMember.concerns.split(',').map(c => c.trim()).filter(c => c.length > 0)
        : []
    }

    const updatedMembers = [...familyMembers, member]
    setFamilyMembers(updatedMembers)
    saveToStorage(STORAGE_KEYS.FAMILY_MEMBERS, updatedMembers)
    
    setNewMember({
      name: "",
      relationship: "",
      age: "",
      status: "",
      concerns: ""
    })
    setIsAddDialogOpen(false)
  }

  const handleRemoveMember = (id: string) => {
    const updatedMembers = familyMembers.filter(member => member.id !== id)
    setFamilyMembers(updatedMembers)
    saveToStorage(STORAGE_KEYS.FAMILY_MEMBERS, updatedMembers)
  }

  const handleAddTask = () => {
    if (!newTask.title.trim() || !newTask.assignedTo.trim() || !newTask.dueDate.trim()) {
      return // Basic validation
    }

    const task: FamilyTask = {
      id: Date.now().toString(),
      title: newTask.title.trim(),
      assignedTo: newTask.assignedTo,
      dueDate: newTask.dueDate,
      status: "not-started",
      priority: newTask.priority,
    }

    const updatedTasks = [...familyTasks, task]
    setFamilyTasks(updatedTasks)
    saveToStorage(STORAGE_KEYS.FAMILY_TASKS, updatedTasks)
    
    setNewTask({
      title: "",
      assignedTo: "",
      dueDate: "",
      priority: "medium"
    })
    setIsAddTaskDialogOpen(false)
  }

  const handleTaskStatusChange = (taskId: string, newStatus: FamilyTask['status']) => {
    const updatedTasks = familyTasks.map(task =>
      task.id === taskId ? { ...task, status: newStatus } : task
    )
    setFamilyTasks(updatedTasks)
    saveToStorage(STORAGE_KEYS.FAMILY_TASKS, updatedTasks)
  }

  const handleRemoveTask = (id: string) => {
    const updatedTasks = familyTasks.filter(task => task.id !== id)
    setFamilyTasks(updatedTasks)
    saveToStorage(STORAGE_KEYS.FAMILY_TASKS, updatedTasks)
  }

  const handleAddEvent = () => {
    if (!newEvent.title.trim() || !newEvent.date.trim() || !newEvent.time.trim()) {
      return // Basic validation
    }

    const event: CalendarEvent = {
      id: Date.now().toString(),
      title: newEvent.title.trim(),
      date: newEvent.date,
      time: newEvent.time,
      attendees: newEvent.attendees || "Family",
      category: newEvent.category,
    }

    const updatedEvents = [...calendarEvents, event]
    setCalendarEvents(updatedEvents)
    saveToStorage(STORAGE_KEYS.CALENDAR_EVENTS, updatedEvents)
    
    setNewEvent({
      title: "",
      date: "",
      time: "",
      attendees: "",
      category: "other"
    })
    setIsAddEventDialogOpen(false)
  }

  const handleRemoveEvent = (id: string) => {
    const updatedEvents = calendarEvents.filter(event => event.id !== id)
    setCalendarEvents(updatedEvents)
    saveToStorage(STORAGE_KEYS.CALENDAR_EVENTS, updatedEvents)
  }

  const handleClearAllData = () => {
    if (confirm('Are you sure you want to clear all family data? This cannot be undone.')) {
      setFamilyMembers(DEFAULT_FAMILY_MEMBERS)
      setFamilyTasks(DEFAULT_FAMILY_TASKS)
      setCalendarEvents(DEFAULT_CALENDAR_EVENTS)
      
      saveToStorage(STORAGE_KEYS.FAMILY_MEMBERS, DEFAULT_FAMILY_MEMBERS)
      saveToStorage(STORAGE_KEYS.FAMILY_TASKS, DEFAULT_FAMILY_TASKS)
      saveToStorage(STORAGE_KEYS.CALENDAR_EVENTS, DEFAULT_CALENDAR_EVENTS)
    }
  }

  const resources = [
    {
      title: "Military Family Life Counselors",
      description: "Free counseling services for military families",
      category: "Support",
    },
    {
      title: "School Liaison Officer",
      description: "Help with school transfers and educational planning",
      category: "Education",
    },
    {
      title: "Exceptional Family Member Program",
      description: "Support for families with special needs",
      category: "Special Needs",
    },
    {
      title: "Military Child Education Coalition",
      description: "Resources for military children's education",
      category: "Education",
    },
  ]

  // Show loading state until data is loaded
  if (!isLoaded) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-white">Loading family dashboard...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Family Dashboard</h1>
          <p className="text-slate-400 text-sm mt-1">
            Data is automatically saved to your browser's local storage
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleClearAllData}
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            Reset Data
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Users className="w-4 h-4 mr-2" />
                Add Family Member
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-slate-800 border-slate-700 text-white">
              <DialogHeader>
                <DialogTitle className="text-white">Add Family Member</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name" className="text-slate-300">
                    Full Name *
                  </Label>
                  <Input
                    id="name"
                    value={newMember.name}
                    onChange={(e) => setNewMember({ ...newMember, name: e.target.value })}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="Enter full name"
                  />
                </div>
                
                <div>
                  <Label htmlFor="relationship" className="text-slate-300">
                    Relationship *
                  </Label>
                  <Select value={newMember.relationship} onValueChange={(value) => setNewMember({ ...newMember, relationship: value })}>
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue placeholder="Select relationship" />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-700 border-slate-600">
                      <SelectItem value="Spouse">Spouse</SelectItem>
                      <SelectItem value="Son">Son</SelectItem>
                      <SelectItem value="Daughter">Daughter</SelectItem>
                      <SelectItem value="Parent">Parent</SelectItem>
                      <SelectItem value="Sibling">Sibling</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="age" className="text-slate-300">
                    Age (Optional)
                  </Label>
                  <Input
                    id="age"
                    type="number"
                    value={newMember.age}
                    onChange={(e) => setNewMember({ ...newMember, age: e.target.value })}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="Enter age"
                  />
                </div>

                <div>
                  <Label htmlFor="status" className="text-slate-300">
                    Status
                  </Label>
                  <Select value={newMember.status} onValueChange={(value) => setNewMember({ ...newMember, status: value })}>
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-700 border-slate-600">
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Student">Student</SelectItem>
                      <SelectItem value="Working">Working</SelectItem>
                      <SelectItem value="Retired">Retired</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="concerns" className="text-slate-300">
                    Current Concerns (Optional)
                  </Label>
                  <Textarea
                    id="concerns"
                    value={newMember.concerns}
                    onChange={(e) => setNewMember({ ...newMember, concerns: e.target.value })}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="Enter concerns separated by commas"
                    rows={3}
                  />
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsAddDialogOpen(false)}
                    className="border-slate-600 text-slate-300 hover:bg-slate-700"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleAddMember}
                    className="bg-blue-600 hover:bg-blue-700"
                    disabled={!newMember.name.trim() || !newMember.relationship.trim()}
                  >
                    Add Member
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Family Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {familyMembers.map((member) => (
          <Card key={member.id} className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                <span className="flex items-center">
                  <Heart className="w-5 h-5 mr-2 text-red-400" />
                  {member.name}
                </span>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="border-slate-600 text-slate-300">
                    {member.relationship}
                  </Badge>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleRemoveMember(member.id)}
                    className="h-6 w-6 p-0 text-slate-400 hover:text-red-400 hover:bg-red-900/20"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {member.age && (
                  <div className="flex items-center justify-between">
                    <span className="text-slate-400 text-sm">Age:</span>
                    <span className="text-white text-sm">{member.age}</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-slate-400 text-sm">Status:</span>
                  <Badge variant="secondary" className="text-xs">
                    {member.status}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-400 text-sm">Tasks:</span>
                  <span className="text-white text-sm">
                    {member.completedTasks}/{member.tasks}
                  </span>
                </div>
                <Progress
                  value={member.tasks > 0 ? (member.completedTasks / member.tasks) * 100 : 0}
                  className="h-2"
                />
                {member.concerns.length > 0 && (
                  <div>
                    <span className="text-slate-400 text-sm">Current Concerns:</span>
                    <div className="mt-1 space-y-1">
                      {member.concerns.map((concern, index) => (
                        <div key={index} className="flex items-center">
                          <AlertTriangle className="w-3 h-3 mr-1 text-yellow-400" />
                          <span className="text-yellow-200 text-xs">{concern}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Family Tasks */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center justify-between">
            <span className="flex items-center">
              <CheckCircle className="w-5 h-5 mr-2" />
              Family Tasks
            </span>
            <Dialog open={isAddTaskDialogOpen} onOpenChange={setIsAddTaskDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" className="bg-green-600 hover:bg-green-700">
                  <Plus className="w-4 h-4 mr-1" />
                  Add Task
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-slate-800 border-slate-700 text-white">
                <DialogHeader>
                  <DialogTitle className="text-white">Add Family Task</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="taskTitle" className="text-slate-300">
                      Task Title *
                    </Label>
                    <Input
                      id="taskTitle"
                      value={newTask.title}
                      onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}
                      className="bg-slate-700 border-slate-600 text-white"
                      placeholder="Enter task title"
                    />
                  </div>

                  <div>
                    <Label htmlFor="assignedTo" className="text-slate-300">
                      Assigned To *
                    </Label>
                    <Select value={newTask.assignedTo} onValueChange={(value) => setNewTask({ ...newTask, assignedTo: value })}>
                      <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                        <SelectValue placeholder="Select who this is assigned to" />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        <SelectItem value="Service Member">Service Member</SelectItem>
                        <SelectItem value="Spouse">Spouse</SelectItem>
                        <SelectItem value="Both Parents">Both Parents</SelectItem>
                        <SelectItem value="Family">Whole Family</SelectItem>
                        {familyMembers.map(member => (
                          <SelectItem key={member.id} value={member.name}>{member.name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="dueDate" className="text-slate-300">
                      Due Date *
                    </Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={newTask.dueDate}
                      onChange={(e) => setNewTask({ ...newTask, dueDate: e.target.value })}
                      className="bg-slate-700 border-slate-600 text-white"
                    />
                  </div>

                  <div>
                    <Label htmlFor="priority" className="text-slate-300">
                      Priority
                    </Label>
                    <Select value={newTask.priority} onValueChange={(value: "high" | "medium" | "low") => setNewTask({ ...newTask, priority: value })}>
                      <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setIsAddTaskDialogOpen(false)}
                      className="border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAddTask}
                      className="bg-green-600 hover:bg-green-700"
                      disabled={!newTask.title.trim() || !newTask.assignedTo.trim() || !newTask.dueDate.trim()}
                    >
                      Add Task
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {familyTasks.map((task) => (
            <div key={task.id} className="border border-slate-700 rounded-lg p-4">
              <div className="flex items-start justify-between mb-2">
                <h4 className="text-white font-medium text-sm">{task.title}</h4>
                <div className="flex items-center space-x-2">
                  {task.priority === "high" && <AlertTriangle className="w-4 h-4 text-red-400" />}
                  {task.status === "completed" && <CheckCircle className="w-4 h-4 text-green-400" />}
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleRemoveTask(task.id)}
                    className="h-6 w-6 p-0 text-slate-400 hover:text-red-400 hover:bg-red-900/20"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                    {task.assignedTo}
                  </Badge>
                  <Select value={task.status} onValueChange={(value: FamilyTask['status']) => handleTaskStatusChange(task.id, value)}>
                    <SelectTrigger className="w-32 h-6 text-xs bg-slate-700 border-slate-600">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-700 border-slate-600">
                      <SelectItem value="not-started">Not Started</SelectItem>
                      <SelectItem value="in-progress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <span className="text-slate-400 text-xs">Due: {task.dueDate}</span>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Family Calendar */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center justify-between">
            <span className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Family Calendar & Events
            </span>
            <Dialog open={isAddEventDialogOpen} onOpenChange={setIsAddEventDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                  <Plus className="w-4 h-4 mr-1" />
                  Add Event
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-slate-800 border-slate-700 text-white">
                <DialogHeader>
                  <DialogTitle className="text-white">Add Calendar Event</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="eventTitle" className="text-slate-300">
                      Event Title *
                    </Label>
                    <Input
                      id="eventTitle"
                      value={newEvent.title}
                      onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                      className="bg-slate-700 border-slate-600 text-white"
                      placeholder="Enter event title"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="eventDate" className="text-slate-300">
                        Date *
                      </Label>
                      <Input
                        id="eventDate"
                        type="date"
                        value={newEvent.date}
                        onChange={(e) => setNewEvent({ ...newEvent, date: e.target.value })}
                        className="bg-slate-700 border-slate-600 text-white"
                      />
                    </div>

                    <div>
                      <Label htmlFor="eventTime" className="text-slate-300">
                        Time *
                      </Label>
                      <Input
                        id="eventTime"
                        type="time"
                        value={newEvent.time}
                        onChange={(e) => setNewEvent({ ...newEvent, time: e.target.value })}
                        className="bg-slate-700 border-slate-600 text-white"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="attendees" className="text-slate-300">
                      Attendees
                    </Label>
                    <Input
                      id="attendees"
                      value={newEvent.attendees}
                      onChange={(e) => setNewEvent({ ...newEvent, attendees: e.target.value })}
                      className="bg-slate-700 border-slate-600 text-white"
                      placeholder="Who will attend this event?"
                    />
                  </div>

                  <div>
                    <Label htmlFor="category" className="text-slate-300">
                      Category
                    </Label>
                    <Select value={newEvent.category} onValueChange={(value: CalendarEvent['category']) => setNewEvent({ ...newEvent, category: value })}>
                      <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        <SelectItem value="school">School</SelectItem>
                        <SelectItem value="medical">Medical</SelectItem>
                        <SelectItem value="social">Social</SelectItem>
                        <SelectItem value="work">Work</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setIsAddEventDialogOpen(false)}
                      className="border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAddEvent}
                      className="bg-purple-600 hover:bg-purple-700"
                      disabled={!newEvent.title.trim() || !newEvent.date.trim() || !newEvent.time.trim()}
                    >
                      Add Event
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {calendarEvents.map((event) => {
              const categoryColors = {
                school: "bg-blue-900/20 border-blue-700/30 text-blue-300",
                medical: "bg-green-900/20 border-green-700/30 text-green-300",
                social: "bg-yellow-900/20 border-yellow-700/30 text-yellow-300",
                work: "bg-purple-900/20 border-purple-700/30 text-purple-300",
                other: "bg-gray-900/20 border-gray-700/30 text-gray-300"
              }

              return (
                <div key={event.id} className={`${categoryColors[event.category]} border rounded-lg p-3 relative group`}>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleRemoveEvent(event.id)}
                    className="absolute top-1 right-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 text-slate-400 hover:text-red-400 hover:bg-red-900/20"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                  <h4 className="font-medium text-sm mb-1">{event.title}</h4>
                  <p className="text-xs opacity-80">{event.date} - {event.time}</p>
                  <p className="text-xs mt-1 opacity-90">{event.attendees}</p>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Resources */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <GraduationCap className="w-5 h-5 mr-2" />
            Family Resources
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {resources.map((resource, index) => (
              <div key={index} className="border border-slate-700 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="text-white font-medium text-sm">{resource.title}</h4>
                  <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                    {resource.category}
                  </Badge>
                </div>
                <p className="text-slate-400 text-sm">{resource.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
